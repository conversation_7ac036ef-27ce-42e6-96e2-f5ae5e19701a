<import name="widget-button" src="./base.ux"></import>

<template>
  <widget-button
    is-dark="{{ isDark }}"
    width="{{ 124 }}"
    icon="{{ icon }}"
    background-color="{{ backgroundColor }}"
    color="{{ color }}"
  >
    <block slot="icon">
      <slot name="icon"></slot>
    </block>
    <slot></slot>
  </widget-button>
</template>

<script>
/**
 * @file 按钮组件
 */
export default {
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: <PERSON>olean,
      default: false,
    },
    backgroundColor: {
      type: String,
      default: '',
    },
    color: {
      type: String,
      default: '',
    },
  },
}
</script>
