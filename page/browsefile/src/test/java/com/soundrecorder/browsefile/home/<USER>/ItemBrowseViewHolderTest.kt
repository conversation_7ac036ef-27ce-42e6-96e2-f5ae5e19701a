/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ItemBrowseViewHolderTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/1/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.item

import android.animation.Animator
import android.os.Build
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.base.view.IRecyclerAdapterData
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.ItemBrowseBinding
import com.soundrecorder.browsefile.databinding.ItemPlayAreaBinding
import com.soundrecorder.browsefile.databinding.ItemRecordInfoBinding
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.databean.StartPlayModel
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ItemBrowseViewHolderTest {

    private var mContext: FragmentActivity? = null
    private val taskId = 1234

    class ItemBrowseViewHolderChild(viewBinding: ItemBrowseBinding, mIsGroupingByContact: Boolean) :
        ItemBrowseViewHolder(viewBinding, object : IBrowseViewHolderListener {
            override fun getWindowType(): MutableLiveData<WindowType> = MutableLiveData(WindowType.SMALL)

            override fun getPlayLiveData(): MutableLiveData<StartPlayModel?>  = MutableLiveData()
        }, mIsGroupingByContact) {

        fun setViewModelChild(itemViewModel: ItemBrowseRecordViewModel) {
            setViewModel(itemViewModel)
        }

        fun onRootViewClickChild(view: View) {
            onRootViewClick(view)
        }

        fun onRootViewLongClickChild(view: View): Boolean {
            return onRootViewLongClick(view)
        }

        fun observeDataChild(owner: LifecycleOwner) {
            observeData(owner)
        }

        fun initBasicViewChild() {
            initBasicView()
        }

        fun setTaskIdChild(taskId: Int) {
            this.taskId = taskId
        }

        fun bindOtherInfoChild() {
            bindOtherInfo()
        }

        fun onSeekBarAnimChangedChild(inAnim: Boolean) {
            onSeekBarAnimChanged(inAnim)
        }

        fun bindViewChild() {
            bindView(false)
        }
    }

    @Before
    fun setUp() {
        mContext = Robolectric.buildActivity(FragmentActivity::class.java).setup().get()
    }

    @After
    fun tearDown() {
        mContext = null
        ItemBrowseRecordViewModel.liveEditMode.remove(taskId)
        ItemBrowseRecordViewModel.liveAddFooter.remove(taskId)
    }

    @Test
    fun should_equals_when_setViewModel() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )

        val mediaId = 2077181L
        val fileName = "标准录音 1.mp3"
        val itemViewModel = ItemBrowseRecordViewModel().also {
            it.taskId = taskId
            it.mediaId = mediaId
            it.displayName = fileName
        }
        ItemBrowseRecordViewModel.addAnimatorDisplayName = fileName
        Assert.assertNotNull(ItemBrowseRecordViewModel.addAnimatorDisplayName)
        viewHolder.setViewModelChild(itemViewModel)
        Assert.assertNull(ItemBrowseRecordViewModel.addAnimatorDisplayName)
    }

    @Test
    fun should_equals_when_onRootViewClick() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )
        val mediaId = 2077181L
        val itemViewModel = Mockito.spy(
            ItemBrowseRecordViewModel().also {
                it.taskId = taskId
                it.mediaId = mediaId
            }
        )
        `when`(itemViewModel.fileIsExists()).thenReturn(true)
        viewHolder.setViewModelChild(itemViewModel)
        if (ItemBrowseRecordViewModel.liveEditMode[taskId] == null) {
            ItemBrowseRecordViewModel.liveEditMode[taskId] = MutableLiveData<Boolean>()
        }
        ItemBrowseRecordViewModel.liveEditMode[taskId]?.value = true
        viewHolder.onRootViewClickChild(View(mContext))

        ItemBrowseRecordViewModel.liveEditMode[taskId]?.value = false
        viewHolder.onRootViewClickChild(View(mContext))
    }

    @Test
    fun should_equals_when_onLongViewClick() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )
        viewHolder.setViewModelChild(ItemBrowseRecordViewModel())
        val result = viewHolder.onRootViewLongClickChild(View(mContext))
        Assert.assertTrue(result)
    }

    @Test
    fun should_true_when_ObserveData() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )
        val itemViewModel = Mockito.spy(
            ItemBrowseRecordViewModel().also {
                it.taskId = taskId
            }
        )
        viewHolder.setViewModelChild(itemViewModel)
        if (ItemBrowseRecordViewModel.liveEditMode[taskId] == null) {
            ItemBrowseRecordViewModel.liveEditMode[taskId] = MutableLiveData<Boolean>()
        }
        (mContext as? LifecycleOwner)?.apply {
            viewHolder.observeDataChild(this)
        }
        Assert.assertTrue(ItemBrowseRecordViewModel.liveEditMode[taskId]?.hasObservers() ?: false)
    }

    @Test
    fun should_equals_when_initBasicView() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )
        if (ItemBrowseRecordViewModel.liveEditMode[taskId] == null) {
            ItemBrowseRecordViewModel.liveEditMode[taskId] = MutableLiveData<Boolean>(true)
        }
        if (ItemBrowseRecordViewModel.liveAddFooter[taskId] == null) {
            ItemBrowseRecordViewModel.liveAddFooter[taskId] = MutableLiveData<Int>(-1)
        }
        viewHolder.setTaskIdChild(taskId)
        viewHolder.initBasicViewChild()
        var event = MotionEvent.obtain(
            System.currentTimeMillis(),
            200,
            MotionEvent.ACTION_DOWN,
            200f,
            200f,
            0
        )
        viewHolder.itemView.findViewById<View>(R.id.checkbox_layout).dispatchTouchEvent(event)

        ItemBrowseRecordViewModel.liveEditMode[taskId]?.value = false
        event = MotionEvent.obtain(
            System.currentTimeMillis(),
            200,
            MotionEvent.ACTION_UP,
            200f,
            200f,
            0
        )
        viewHolder.itemView.findViewById<View>(R.id.checkbox_layout).dispatchTouchEvent(event)
        Assert.assertEquals(0, ItemBrowseRecordViewModel.liveAddFooter[taskId]?.value)
    }

    @Test
    fun should_equals_when_bindOtherInfo() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )
        val mediaId = 2077181L
        val fileName = "标准录音 1.mp3"
        val itemViewModel = ItemBrowseRecordViewModel().also {
            it.taskId = taskId
            it.mediaId = mediaId
            it.displayName = fileName
        }
        viewHolder.setViewModelChild(itemViewModel)
        viewHolder.adapterData = object : IRecyclerAdapterData {
            override fun getItemCount(): Int {
                return 1
            }

            override fun getRealPosInViewType(position: Int): Int {
                return 0
            }
        }
        ItemBrowseRecordViewModel.addAnimatorDisplayName = fileName
        Whitebox.invokeMethod<Animator>(viewHolder, "startAddAnimator", viewHolder.itemView, null)
        viewHolder.bindOtherInfoChild()
        Assert.assertEquals(0F, viewHolder.itemView.alpha)
    }

    @Test
    fun should_null_when_onSeekBarAnimChanged() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )
        viewHolder.setViewModelChild(ItemBrowseRecordViewModel())
        Whitebox.invokeMethod<Animator>(viewHolder, "startAddAnimator", viewHolder.itemView, null)
        ItemBrowseRecordViewModel.addAnimatorDisplayName = "asdasd"
        viewHolder.onSeekBarAnimChangedChild(false)
        Assert.assertNull(ItemBrowseRecordViewModel.addAnimatorDisplayName)
    }

    @Test
    fun should_notNull_when_startAddAnimator() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )
        val animator = Whitebox.invokeMethod<Animator>(
            viewHolder,
            "startAddAnimator",
            viewHolder.itemView,
            null
        )
        Assert.assertNotNull(animator)
    }

    @Test
    fun should_equals_when_bindView() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )
        val title = "标准录音 2.mp3"
        val itemViewModel = ItemBrowseRecordViewModel().also {
            it.title = title
            it.taskId = taskId
        }
        viewHolder.setViewModelChild(itemViewModel)
        viewHolder.bindViewChild()
        val recordInfo =
            Whitebox.getInternalState<ItemRecordInfoBinding>(viewHolder, "mItemRecordInfo")
        Assert.assertEquals(title, recordInfo.recordTitle.text)
    }

    @Test
    fun should_equals_when_updateCheckBox() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )
        val title = "标准录音 2.mp3"
        val itemViewModel = ItemBrowseRecordViewModel().also {
            it.title = title
            it.taskId = taskId
        }
        viewHolder.setViewModelChild(itemViewModel)
        if (ItemBrowseRecordViewModel.liveEditMode[taskId] == null) {
            ItemBrowseRecordViewModel.liveEditMode[taskId] = MutableLiveData<Boolean>(true)
        }
        ItemBrowseRecordViewModel.editModeChangeAnim = true
        Whitebox.invokeMethod<Void>(viewHolder, "updateCheckbox")

        ItemBrowseRecordViewModel.editModeChangeAnim = false
        Whitebox.invokeMethod<Void>(viewHolder, "updateCheckbox")

        ItemBrowseRecordViewModel.liveEditMode[taskId]?.value = false
        Whitebox.invokeMethod<Void>(viewHolder, "updateCheckbox")

        ItemBrowseRecordViewModel.editModeChangeAnim = true
        Whitebox.invokeMethod<Void>(viewHolder, "updateCheckbox")
        val playArea = Whitebox.getInternalState<ItemPlayAreaBinding>(viewHolder, "mItemPlayArea")
        Assert.assertEquals(View.VISIBLE, playArea.playButtonArea.visibility)
    }

    @Test
    fun should_not_null_when_********************************() {
        val viewHolder = ItemBrowseViewHolder(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ), object : IBrowseViewHolderListener {

                override fun getWindowType(): MutableLiveData<WindowType> = MutableLiveData(WindowType.SMALL)

                override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> = MutableLiveData()
            },
            false
        )
        val animator =
            Whitebox.invokeMethod<Animator>(viewHolder, "********************************")
        Assert.assertNotNull(animator)
    }


    @Test
    fun should_null_when_********************************() {
        val viewHolder = ItemBrowseViewHolder(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ), object : IBrowseViewHolderListener {

                override fun getWindowType(): MutableLiveData<WindowType> = MutableLiveData(WindowType.LARGE)

                override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> = MutableLiveData()
            },
            false
        )
        val animator =
            Whitebox.invokeMethod<Animator>(viewHolder, "********************************")
        Assert.assertNull(animator)
    }

    @Test
    fun should_not_null_when_getPlayBtnEditModeExitAnimation() {
        val viewHolder = ItemBrowseViewHolder(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ), object : IBrowseViewHolderListener {

                override fun getWindowType(): MutableLiveData<WindowType> = MutableLiveData(WindowType.SMALL)

                override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> = MutableLiveData()
            },
            false
        )
        val animator =
            Whitebox.invokeMethod<Animator>(viewHolder, "getPlayBtnEditModeExitAnimation")
        Assert.assertNotNull(animator)
    }

    @Test
    fun should_null_when_getPlayBtnEditModeExitAnimation() {
        val viewHolder = ItemBrowseViewHolder(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ), object : IBrowseViewHolderListener {

                override fun getWindowType(): MutableLiveData<WindowType> = MutableLiveData(WindowType.MIDDLE)

                override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> = MutableLiveData()
            },
            false
        )
        val animator =
            Whitebox.invokeMethod<Animator>(viewHolder, "getPlayBtnEditModeExitAnimation")
        Assert.assertNull(animator)
    }

    @Test
    fun should_equals_when_onViewRecycled() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false
            ),
            false
        )
        if (ItemBrowseRecordViewModel.liveEditMode[taskId] == null) {
            ItemBrowseRecordViewModel.liveEditMode[taskId] = MutableLiveData<Boolean>(true)
        }
        viewHolder.setViewModelChild(ItemBrowseRecordViewModel())
        viewHolder.onViewRecycled()
        Assert.assertEquals(false, ItemBrowseRecordViewModel.liveEditMode[taskId]?.hasObservers())
    }

    @Test
    fun should_equals_when_updateItemSelectBg() {
        val viewHolder = ItemBrowseViewHolderChild(
            DataBindingUtil.inflate(
                LayoutInflater.from(mContext), R.layout.item_browse, FrameLayout(mContext!!), false), false)
        Whitebox.invokeMethod<Unit>(viewHolder, "updateItemSelectBg", null as? WindowType, null as? StartPlayModel)
        var itemView = Whitebox.getInternalState<View>(viewHolder, "mCardView")
        Assert.assertFalse(itemView.isSelected)

        viewHolder.setViewModelChild(ItemBrowseRecordViewModel().apply { mediaId = 1000 })
        Whitebox.invokeMethod<Unit>(viewHolder, "updateItemSelectBg", WindowType.LARGE, StartPlayModel(mediaId = 1000))
        itemView = Whitebox.getInternalState<View>(viewHolder, "mCardView")
        Assert.assertTrue(itemView.isSelected)
    }
}