package com.soundrecorder.browsefile.search.load.center

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.CancellationSignal
import androidx.core.os.bundleOf
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.browsefile.search.load.SearchRepository
import com.soundrecorder.browsefile.shadows.ShadowCenterDbUtils
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.constant.RecordModeConstant
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowCenterDbUtils::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class CenterDbManagerTest {

    private var mContext: Context? = null
    private var mMockApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setup() {
        ShadowLog.stream = System.out
        mContext = Mockito.spy(ApplicationProvider.getApplicationContext<Context>())
        mMockApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockApplication!!.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mContext)
    }

    @After
    fun release() {
        mMockApplication?.close()
        mMockApplication = null
        mContext = null
    }

    @Test
    fun shouldReturnTrue_when_insertOrUpdateDmp() {
        val result = CenterDbManager.insertOrUpdateDmp(arrayListOf())
        Assert.assertTrue(result)
    }

    @Test
    fun shouldReturnTrue_when_deleteByMediaId() {
        val result = CenterDbManager.deleteByMediaId(arrayListOf())
        Assert.assertTrue(result)
    }

    @Test
    fun should_returnBucket_when_getRealSupportFilter() {
        val bucketList = Whitebox.invokeMethod<String>(
            CenterDbManager,
            "getRealSupportFilter",
            RecordModeConstant.BUCKET_VALUE_ALL.toString())
        Assert.assertNotNull(bucketList)
    }

    @Test
    fun shouldReturnEmptyExp_when_getCompleteFlag() {
        FeatureOption.OPLUS_VERSION_EXP = true
        val convertMap = Whitebox.invokeMethod<Map<Int, Int>>(CenterDbManager, "getCompleteFlag")
        Assert.assertEquals(0, convertMap.size)
    }

    @Test
    fun shouldReturnTrue_when_initSearchProvider() {
        val mockCenterDbManager = Mockito.mockStatic(CenterDbManager::class.java)
        Mockito.`when`(CenterDbManager.initSearchProvider()).thenReturn(true)
        Assert.assertTrue(CenterDbManager.initSearchProvider() ?: false)
        mockCenterDbManager.close()
    }

    @Test
    fun should_returnCorrect_when_calSearchSerategyVersion() {
        val finalVersion = Whitebox.invokeMethod<Int>(
            CenterDbManager,
            "calSearchSerategyVersion",
            100, 100)
        Assert.assertEquals(100, finalVersion)
    }

    @Test
    fun should_returnCorrect_when_parseResultCodeIsSuccess() {
        val methodName = "parseResultCodeIsSuccess"

        val mockCenterDbManager = Mockito.mock(CenterDbManager::class.java)
        val emptyBundle: Bundle? = null
        val isSuccessWhenEmpty = Whitebox.invokeMethod<Boolean>(mockCenterDbManager,
            methodName, emptyBundle)
        Assert.assertTrue(isSuccessWhenEmpty)

        val isSuccessWhenNoCode = Whitebox.invokeMethod<Boolean>(mockCenterDbManager,
            methodName, Bundle())
        Assert.assertFalse(isSuccessWhenNoCode)

        val isSuccessWhenCodeIsZero = Whitebox.invokeMethod<Boolean>(mockCenterDbManager,
            methodName, bundleOf("code" to 0))
        Assert.assertTrue(isSuccessWhenCodeIsZero)

        val isSuccessWhenCodeIsNotZero = Whitebox.invokeMethod<Boolean>(mockCenterDbManager,
            methodName, bundleOf("code" to 1))
        Assert.assertFalse(isSuccessWhenCodeIsNotZero)
    }

    @Test
    fun should_notNull_when_doSearch() {
        Whitebox.setInternalState(CenterDbManager.javaClass, "isSearchServiceInit", true)
        val mockContentResolver = Mockito.spy(ContentResolver::class.java)
        /*val mockContentProviderClient = Mockito.spy(ContentProviderClient::class.java)

        val versionBundle = bundleOf("serviceStatus" to true, "indexVersion" to bundleOf("recorder" to 100), "searchStrategyVersion" to 100)
        val resourceBundle = bundleOf("resourceVersion" to 100)
        Mockito.`when`(mockContentProviderClient.call(Mockito.anyString(), Mockito.anyString(), Mockito.any(Bundle::class.java))).thenReturn(
            versionBundle, resourceBundle)
        Mockito.`when`(mockContentResolver.acquireUnstableContentProviderClient(Mockito.anyString()))
            .thenReturn(mockContentProviderClient)
        Mockito.`when`(mContext?.contentResolver).thenReturn(mockContentResolver)*/

        val mockCursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`(
            mockContentResolver.query(
                Mockito.any(Uri::class.java), Mockito.any(), Mockito.any(Bundle::class.java), Mockito.any(CancellationSignal::class.java)))
            .thenReturn(mockCursor)

        val result = CenterDbManager.doSearch(mutableMapOf(SearchRepository.KEY_SEARCH_WORD to ""), 0, 0, "")
        Assert.assertNotNull(result)
    }
}