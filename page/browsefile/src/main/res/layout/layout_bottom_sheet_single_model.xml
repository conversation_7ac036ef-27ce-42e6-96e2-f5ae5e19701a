<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/normal_bottom_sheet_toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp50"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:textSize="@dimen/dp18"
        android:textColor="@color/coui_color_primary_neutral"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.COUIRecyclerView
        android:id="@+id/itemList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/bottom_sheet_single_model_padding"
        app:layout_constraintTop_toBottomOf="@+id/normal_bottom_sheet_toolbar" />

</androidx.constraintlayout.widget.ConstraintLayout>