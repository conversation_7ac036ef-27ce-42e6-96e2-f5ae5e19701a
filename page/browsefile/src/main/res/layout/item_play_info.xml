<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/seek_bar_area"
    android:layout_width="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:visibility="visible"
    tools:visibility="visible">

    <Space
        android:id="@+id/space_play_info"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <FrameLayout
        android:id="@+id/seek_bar_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/space_play_info"
        app:layout_constraintStart_toEndOf="@+id/playProgress"
        app:layout_constraintEnd_toStartOf="@+id/play_total_duration"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingHorizontal="@dimen/dp9">
        <com.soundrecorder.browsefile.home.view.ItemBrowsePlayCustomSeekBar
            android:id="@+id/seek_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            app:couiSeekBarProgressColor="@color/seek_bar_progress"
            app:couiSeekBarThumbColor="@color/seek_bar_thumb"
            app:thumbBordersColor="@color/seek_bar_thumb_border"
            app:thumbBordersSize="@dimen/dp2"
            app:couiSeekBarThumbShadowSize="@dimen/dp2"
            app:couiSeekBarShadowColor="@color/seek_bar_thumb_shadow"
            app:couiSeekBarBackgroundHeight="@dimen/dp4"
            app:couiSeekBarProgressHeight="@dimen/dp4"
            app:couiSeekBarBackGroundEnlargeScale="6"
            android:layoutDirection="locale"/>
    </FrameLayout>

    <TextView
        android:id="@+id/playProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        app:layout_constraintTop_toBottomOf="@id/space_play_info"
        app:layout_constraintStart_toStartOf="parent"
        android:textColor="@color/item_record_extra_info_color"
        app:layout_constraintTop_toTopOf="@+id/seek_bar_content"
        app:layout_constraintBottom_toBottomOf="@+id/seek_bar_content"
        style="@style/couiTextAppearanceArticleBody"
        android:textSize="@dimen/text_item_play_progress_size"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        android:textFontWeight="500"
        android:lineHeight="@dimen/sp16"
        tools:text="00:00" />

    <TextView
        android:id="@+id/play_total_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/space_play_info"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/seek_bar_content"
        app:layout_constraintTop_toTopOf="@+id/seek_bar_content"
        app:layout_constraintBottom_toBottomOf="@+id/seek_bar_content"
        android:textSize="@dimen/text_item_play_progress_size"
        android:text="@{itemRecord.durationText()}"
        android:textColor="@color/item_record_extra_info_color"
        android:lineHeight="@dimen/sp16"
        style="@style/couiTextAppearanceArticleBody"
        android:fontFamily="sys-sans-en"
        android:textFontWeight="500"
        android:fontFeatureSettings="tnum"
        tools:text="00:00" />
</merge>