<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="@dimen/dp15"
        android:paddingBottom="@dimen/dp18">

        <com.coui.appcompat.progressbar.COUILottieLoadingView
            android:id="@+id/progress_bar"
            style="@style/Widget.COUI.COUILottieLoadingView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />

        <TextView
            android:id="@+id/record_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp10"
            android:singleLine="true"
            android:text="@string/oplus_loading_dialog_text_view"
            android:textColor="@color/percent_30_black"
            android:textSize="@dimen/sp14" />
    </LinearLayout>
</layout>