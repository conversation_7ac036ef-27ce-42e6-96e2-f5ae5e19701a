<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/coordinator"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".home.call.CallRecordListFragment"
        android:background="@color/common_background_color">

        <FrameLayout
            android:id="@+id/contentView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appBarLayout"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.recyclerview.widget.COUIRecyclerView
                android:id="@+id/rv_call_record_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:divider="@null"
                android:importantForAccessibility="no"
                android:requiresFadingEdge="vertical"
                android:scrollbars="none"
                tools:listitem="@layout/item_browse" />

            <ViewStub
                android:id="@+id/mEmptyViewStub"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:inflatedId="@+id/empty_recorder_layout"
                android:layout="@layout/layout_empty_recorder"
                android:visibility="gone"
                tools:visibility="visible" />

            <ViewStub
                android:id="@+id/mLoadingStub"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout="@layout/loading_view"
                android:visibility="gone" />

            <ViewStub
                android:id="@+id/mPermissionDeniedStub"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:inflatedId="@+id/permission_denied_layout"
                android:layout="@layout/layout_permission_denied"
                android:visibility="gone"
                tools:visibility="gone" />

            <ViewStub
                android:id="@+id/mViewStub"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:inflatedId="@+id/navi_layout"
                android:layout="@layout/navigation_view"
                android:visibility="gone" />
        </FrameLayout>

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            app:elevation="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/recorder_toolbar_height"
                app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

            <View
                android:id="@+id/divider_line"
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_background_height"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="@dimen/common_margin"
                android:layout_marginRight="@dimen/common_margin"
                android:alpha="0"
                android:background="?attr/couiColorDivider"
                android:forceDarkAllowed="false"
                tools:ignore="UnusedAttribute" />

        </com.google.android.material.appbar.AppBarLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

</layout>