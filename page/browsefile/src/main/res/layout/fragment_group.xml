<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="viewModel"
            type="com.soundrecorder.browsefile.home.load.BrowseViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinator"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/couiColorBackgroundWithCard">

        <com.google.android.material.appbar.COUIDividerAppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            app:elevation="0dp">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                style="@style/COUIToolBarInAppBarLayoutStyle"
                android:layout_width="match_parent"
                app:layout_scrollFlags="noScroll"
                app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

        </com.google.android.material.appbar.COUIDividerAppBarLayout>

        <androidx.recyclerview.widget.COUIRecyclerView
            android:id="@+id/rv_group_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="top|center_horizontal"
            android:clipToPadding="false"
            android:divider="@null"
            android:forceDarkAllowed="false"
            android:paddingStart="16dp"
            android:paddingTop="@dimen/coui_list_to_ex_top_padding"
            android:paddingEnd="16dp"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

        <FrameLayout
            android:id="@+id/navi_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:visibility="gone"
            android:background="?attr/couiColorBar">

            <com.coui.appcompat.bottomnavigation.COUINavigationView
                android:id="@+id/group_navi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:couiItemLayoutType="verticalType"
                app:couiNaviIconTint="@null"
                app:couiNaviMenu="@menu/group_menu_main_navi"
                app:couiNaviTextColor="@color/navi_group_item_text_color"
                app:couiToolNavigationViewBg="?attr/couiColorBar"
                app:navigationType="tool" />
        </FrameLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>