/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: GroupChooseFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/02/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  ******** 2025/02/20      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.view

import android.annotation.SuppressLint
import android.graphics.Canvas
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.home.view.group.GroupManageRecyclerAdapter
import com.soundrecorder.browsefile.home.view.group.entity.GroupItem
import com.soundrecorder.browsefile.home.view.group.entity.GroupRecyclerItemListener
import com.soundrecorder.browsefile.home.view.group.util.GroupItemConverter
import com.soundrecorder.browsefile.home.view.group.util.GroupManageUtils
import com.soundrecorder.browsefile.home.view.group.util.GroupManageUtils.scrollToPosition
import com.soundrecorder.browsefile.home.view.group.util.GroupViewModel
import com.soundrecorder.browsefile.home.view.group.util.RecordGroupItemManipulator
import com.soundrecorder.browsefile.home.view.group.view.GroupFragment.Companion.MAX_CUSTOM_GROUP_INFO_NUM
import com.soundrecorder.browsefile.parentchild.BrowseFileActivityViewModel
import com.soundrecorder.common.buryingpoint.BuryingPoint.addCreateNewGroup
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.db.GroupInfoManager
import java.lang.ref.WeakReference

class GroupChooseFragment : COUIPanelFragment() {
    companion object {
        private const val TAG = "GroupChooseFragment"
        private var DARK_ITEM_BG_COLOR = Color.argb(
            255,
            52,
            52,
            52
        )

        fun show(
            fm: FragmentManager,
        ): COUIBottomSheetDialogFragment {
            val sdf = COUIBottomSheetDialogFragment()
            sdf.setMainPanelFragment(newInstance())
            sdf.setIsShowInMaxHeight(true)
            sdf.show(fm, GroupChooseFragment::class.java.name)
            return sdf
        }

        private fun newInstance(): GroupChooseFragment {
            val fragment = GroupChooseFragment()
            val args = Bundle()
            fragment.arguments = args
            return fragment
        }
    }

    private val groupInfoManager =
        GroupInfoManager.getInstance(context ?: BaseApplication.getAppContext())
    private var groupManageRecyclerAdapter: GroupManageRecyclerAdapter? = null
    private var recyclerView: RecyclerView? = null

    private val nim = RecordGroupItemManipulator(this)

    private val groupViewModel: GroupViewModel by activityViewModels()
    private val mBrowseFileActivityViewModel: BrowseFileActivityViewModel by activityViewModels()

    private val onMoveGroupListener = GroupChooseMoveListener(this)

    override fun initView(panelView: View?) {
        super.initView(panelView)
        val bgColor =
            COUIContextUtil.getAttrColor(
                context,
                com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard
            )
        panelView?.setBackgroundColor(bgColor)
        LayoutInflater.from(context)
            .inflate(R.layout.fragment_group_choose, contentView as? ViewGroup, true)

        initToolbar()
        initRecyclerViewAndDividerLine(panelView)
        initScrollToPosition()
    }

    private fun initToolbar() {
        toolbar.apply {
            visibility = View.VISIBLE
            title = context.getString(com.soundrecorder.common.R.string.recording_move_to_group)
            isTitleCenterStyle = true
            inflateMenu(R.menu.group_menu_move)
            menu.findItem(R.id.group_menu_move_save).apply {
                title = ""
                isEnabled = false
                icon = null
            }
            menu.findItem(R.id.group_menu_move_cancel).setOnMenuItemClickListener {
                triggerCancel()
                true
            }
        }
    }

    private fun updateChosenGroup(newGroupInfo: GroupInfo?) {
        if (newGroupInfo != null && newGroupInfo.mUuId != groupViewModel.currentGroup.value?.mUuId) {
            val isSelectedListFromMedia = groupViewModel.currentGroup.value?.isDefaultGroup() ?: false
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                groupInfoManager.moveRecordsByGroupInfo(
                    groupViewModel.mutableSelectRecordings.value,
                    isSelectedListFromMedia,
                    newGroupInfo,
                    onMoveGroupListener
                )
            }
        }
    }

    private fun triggerCancel() {
        DebugUtil.d(TAG, "triggerCancel")
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    private fun initRecyclerViewAndDividerLine(root: View?) {
        if (root == null) {
            return
        }
        val divider: View = root.findViewById(R.id.group_choose_divider)
        recyclerView = root.findViewById(R.id.choose_group_recycle_view)
        recyclerView?.layoutManager = object : LinearLayoutManager(context) {
            override fun onLayoutCompleted(state: RecyclerView.State?) {
                super.onLayoutCompleted(state)
                // check if the divider should be visible
                val last = findLastCompletelyVisibleItemPosition()
                val first = findFirstCompletelyVisibleItemPosition()
                val size = recyclerView?.adapter?.itemCount ?: -1
                divider.alpha = if (last < size - 1 || first > 0) 1f else 0f
            }
        }
        groupManageRecyclerAdapter = GroupManageRecyclerAdapter(
            root.context,
            GroupRecyclerItemListener(
                onSelectedChangedListener = { target, adapterSelectedCallback ->
                    groupViewModel.isMovedGroup.postValueSafe(true)
                    triggerSelectGroup(target, adapterSelectedCallback)
                },
                onCheckedChangedListener = null,
                onCreateNewGroupClickListener = {
                    addCreateNewGroup()
                    if ((GroupInfoDbUtil.getCustomGroupsFromDB(context).size < MAX_CUSTOM_GROUP_INFO_NUM)) {
                        GroupEditFragment.show(childFragmentManager, null)
                    } else {
                        ToastManager.showShortToast(
                            context,
                            com.soundrecorder.common.R.string.maximum_custom_group_limit
                        )
                    }
                })
        )
        recyclerView?.adapter = groupManageRecyclerAdapter
        recyclerView?.addItemDecoration(BackgroundItemDecoration())
    }

    private fun triggerSelectGroup(target: GroupItem?, adapterSelectedCallback: () -> Unit) {
        val selectedItem = groupManageRecyclerAdapter?.getCurrentValues()?.find { it.selected }
        val targets = target?.let { listOf(it) } ?: emptyList()
        if (targets.isEmpty()) {
            return
        }
        nim.select(selectedItem, targets) { success ->
            if (success) {
                selectedItem?.selected = false
                adapterSelectedCallback.invoke()
            }
        }
        updateChosenGroup(targets[0].groupInfo)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initObservers()
    }

    @SuppressLint("NewApi")
    private fun initObservers() {
        groupViewModel.groupsInDb.observe(viewLifecycleOwner) { groupInfos ->
            groupManageRecyclerAdapter?.refresh(
                recyclerView,
                GroupItemConverter.createChosenTransformer(
                    groupViewModel,
                    groupInfos
                )
            )
        }
        groupInfoManager.mAllGroupInfoList.observe(viewLifecycleOwner) {
            groupViewModel.groupsInDb.value = it
            mBrowseFileActivityViewModel.liveDataGroupList.value = it
        }
        //初次进来先初始化数据。
        groupViewModel.groupsInDb.value = groupInfoManager.getAllGroupInfoList()
    }

    private fun initScrollToPosition() {
        if (recyclerView == null) {
            DebugUtil.w(
                TAG,
                "initScrollToPosition recyclerView is null! Don't scroll to position!"
            )
        } else {
            GroupManageUtils.OneShotPostDrawListener.add(
                recyclerView!!,
                scrollToPosition(
                    recyclerView!!,
                    mBrowseFileActivityViewModel.currentGroup.value,
                    groupManageRecyclerAdapter
                )
            )
        }
    }

    inner class BackgroundItemDecoration :
        COUIRecyclerView.COUIRecyclerViewItemDecoration(context) {
        override fun onDrawOver(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
            super.onDrawOver(c, parent, state)
            if (NightModeUtil.isNightMode(parent.context)) {
                val childCount = parent.childCount
                for (i in 0 until childCount) {
                    val view = parent.getChildAt(i)
                    //只设置分组item。
                    if (view.findViewById<TextView>(R.id.create_group_item) == null) {
                        view.setBackgroundColor(
                            DARK_ITEM_BG_COLOR
                        )
                    }
                }
            }
        }
    }

    private class GroupChooseMoveListener(fragment: GroupChooseFragment) : GroupInfoManager.MoveGroupListener {
        private val weakFragment = WeakReference(fragment)

        override fun onPreMoveGroup(showWaitingDialog: Boolean) {
            if (showWaitingDialog) {
                val fragment = weakFragment.get() ?: return
                if (fragment.isDetached) return
                fragment.groupViewModel.onMoveGroupCallBack.value?.onPreMoveGroup()
            }
        }

        override fun onMovingGroup(currentIndex: Int, totalCount: Int) {}

        override fun onPostMoveGroup(movedState: Int, movedRecordList: ArrayList<Record>?, showWaitingDialog: Boolean) {
            val fragment = weakFragment.get() ?: return
            if (fragment.isDetached || fragment.activity == null) return
            if (movedState == GroupInfoManager.MOVE_GROUP_STATE_SUCCESS) {
                ToastManager.showShortToast(
                    BaseApplication.getAppContext(),
                    com.soundrecorder.common.R.string.recording_move_to_group_success
                )
                fragment.groupViewModel.onMoveGroupCallBack.value?.onMovedGroup(showWaitingDialog)
                fragment.activity?.runOnUiThread {
                    fragment.triggerCancel()
                }
            } else {
                DebugUtil.e(TAG, "onPostMoveGroup Failed to move the group movedState:$movedState!")
            }
        }
    }

    interface OnMoveGroupCallBack {
        fun onPreMoveGroup()
        fun onMovedGroup(showWaitingDialog: Boolean = false)
    }
}