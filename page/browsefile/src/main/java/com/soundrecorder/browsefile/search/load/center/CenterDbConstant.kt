package com.soundrecorder.browsefile.search.load.center

import android.provider.MediaStore
import androidx.collection.arrayMapOf
import com.soundrecorder.browsefile.search.utils.ForNoteUtil

object CenterDbConstant {
    // 录音对外中子提供的provider-authority
    const val AUTHORITY_SEARCH_CENTER = "com.oplus.soundrecorder.searchcenter.provider"

    // 中子包名
    const val CENTER_DMP_PKG_NAME = "com.oplus.dmp"

    // 访问中子接口，需传给中子值
    const val PROVIDER_INDEX = "recorder"
    const val PROVIDER_UPDATE_PAGE_SIZE = 500

    // 同步数据给中子，需要从媒体库查询的字段
    val RECODER_PROJECTION = arrayOf(
        MediaStore.Audio.Media._ID,
        MediaStore.Audio.Media.DATA,
        MediaStore.Audio.Media.DISPLAY_NAME,
        MediaStore.Audio.Media.SIZE,
        MediaStore.Audio.Media.DATE_MODIFIED,
        MediaStore.Audio.Media.DURATION,
        MediaStore.Audio.Media.RELATIVE_PATH,
        MediaStore.Audio.Media.MIME_TYPE
    )

    /*中子索引provider，提供构造建立索引功能*/
    object IndexProvider {
        const val INDEX_PROVIDER_AUTHORITY = "com.oplus.oss.provider.IndexProvider"

        // 添加数据
        const val CALL_METHOD_ADD = "add"

        // 更新数据
        const val CALL_METHOD_UPDATE = "update"

        // 与中子同事沟通，为update的加强方法。更新数据，并删掉没有的索引。
        const val CALL_METHOD_DAILY_TASK = "dailyTask"

        // 删除数据
        const val CALL_METHOD_DELETE = "delete"

        // 获取中台对应资源的资源版本 resourceVersion
        const val CALL_METHOD_INFO = "info"

        // 调用indexProvider add/delete/update 方法需传递的资源版本号
        // 通过min(录音支持最大资源版本号，获取当前中子最大资源版本号)
        const val CALL_PARAM_VERSION = "resourceVersion"

        /**
         * 传递给中子数据源字段
         * 为向下兼容不影响搜索功能。该数据字段只能增加，不能减少，后期不用字段，可以传空字符
         */
        // the value contains:1-standard、2-interview、3-meeting、4-call
        // see @com.soundrecorder.base.model.constant.RecorderConstants-RECORD_TYPE
        const val COLUMN_NAME_BUCKET = "bucket"
        const val COLUMN_NAME_MEDIA_PATH = "media_path"
        const val COLUMN_NAME_CONVERT_PATH = "text_path"
        const val COLUMN_NAME_MEDIA_ID = "id"
        const val COLUMN_NAME_DISPLAY_NAME = "display_name"
        const val COLUMN_NAME_SIZE = "size"
        const val COLUMN_NAME_DATE_MODIFIED = "date_modified"
        const val COLUMN_NAME_DURATION = "duration"
        const val COLUMN_NAME_EXTEND = "extend"
        const val SUMMARY_TEXT_FLAG = "summary_text_flag"
        val CENTER_PROJECTION = if (ForNoteUtil.isSupportNeutronVersion()) {
            arrayOf(
                COLUMN_NAME_BUCKET,
                COLUMN_NAME_MEDIA_PATH,
                COLUMN_NAME_CONVERT_PATH,
                COLUMN_NAME_MEDIA_ID,
                COLUMN_NAME_DISPLAY_NAME,
                COLUMN_NAME_SIZE,
                COLUMN_NAME_DATE_MODIFIED,
                COLUMN_NAME_DURATION,
                COLUMN_NAME_EXTEND,
                SUMMARY_TEXT_FLAG
            )
        } else {
            arrayOf(
                COLUMN_NAME_BUCKET, COLUMN_NAME_MEDIA_PATH, COLUMN_NAME_CONVERT_PATH,
                COLUMN_NAME_MEDIA_ID, COLUMN_NAME_DISPLAY_NAME, COLUMN_NAME_SIZE,
                COLUMN_NAME_DATE_MODIFIED, COLUMN_NAME_DURATION, COLUMN_NAME_EXTEND
            )
        }
    }

    /*中子搜索provider，提供检索功能*/
    object SearchProvider {
        const val SEARCH_PROVIDER_AUTHORITY = "com.oplus.oss.provider.SearchProvider"

        // 获取中台支持的最高检索策略版本 searchVersion
        const val CALL_METHOD_INFO = "info"

        const val COLUMN_NAME_SEARCH_RESULT_HIGHLIGHT = "highlight"
    }

    /**
     * 调用中子接口，中子返回bundle中子code值
     */
    object ResultCode {
        //0表示正常
        const val CODE_SUCCESS = 0

        //searchProvider:表示本次搜索是7天未搜索后的首次搜索
        //indexProvider: 同步建立索引失败：索引请求因7天未搜索被忽略
        const val CODE_ERROR_SLEEP = 1

        // 业务被禁用
        const val CODE_ERROR_FORBIDDEN = 100
    }


    /**
     * 资源版本(resourceVersion):资源方（录音）提供的原始数据版本，默认100，涉及中子构建索引时需修改，按101、102递增
     * 索引版本(indexVersion):基于资源版本构建的中台倒排索引数据库版本,默认100，涉及到资源版本变更或检索版本变更，可能需要修改，按101、102递增
     * 检索版本(searchVersion):检索方(录音)向中台发起检索请求时的版本号，不同版本体现再检索能力的变化，也可能于不同的索引版本有关
     */
    object Version {
        // 所有资源版本列表
        const val RESOURCE_VERSION_DEFAULT_100: Int = 100
        const val SEARCH_VERSION_DEFAULT_100: Int = 100

        //当前录音版本支持最大资源版本号，录音版本升级，提供给中子的字段只能增加不能删除已有字段
        const val RECORDER_RESOURCE_VERSION = RESOURCE_VERSION_DEFAULT_100

        //当前录音版本支持最大检索版本
        const val RECORDER_SEARCH_VERSION = SEARCH_VERSION_DEFAULT_100

        // 当前录音版本支持的所有中子索引版本，同中子索引版本号保持一致
        val RECORDER_RESOURCE_VERSION_LIST = intArrayOf(RESOURCE_VERSION_DEFAULT_100)

        //中台已构建索引的版本号iv支持的检索策略版本列表 key: searchVersion; value:对应的indexVersion
        // 该数据由中子提供，涉及中子升级时修改
        val SEARCH_SUPPORT_INDEX_LIST = arrayMapOf<Int, IntArray>().apply {
            this[SEARCH_VERSION_DEFAULT_100] = intArrayOf(RESOURCE_VERSION_DEFAULT_100)
        }
    }
}