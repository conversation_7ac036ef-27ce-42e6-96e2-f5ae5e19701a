/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/8/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.soundrecorder.base.utils.DebugUtil

class SearchPagingSource(
    private val searchRepository: SearchRepository,
    private val searchValues: MutableMap<String, String>? = null
) : PagingSource<Int, ItemSearchViewModel>() {

    companion object {
        private const val TAG = "SearchPagingSource"
    }

    override fun getRefreshKey(state: PagingState<Int, ItemSearchViewModel>): Int? {
        return state.anchorPosition
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ItemSearchViewModel> {
        return try {
            val searchWrapper =
                searchRepository.query(
                    searchValues,
                    params.loadSize,
                    params.key ?: SearchRepository.PAGE_START_INDEX
                )
            DebugUtil.d(
                TAG,
                "load: params is ${params.key}, nextPageNo is ${searchWrapper.nextPageNo}, totalCount is ${searchWrapper.totalCount}"
            )

            //notify totalCount has changed
            searchRepository.postTotalCount(searchWrapper)
            //notify search finished
            searchRepository.cancelQueryTimeOut()
            LoadResult.Page(searchWrapper.pageData, null, searchWrapper.nextPageNo)
        } catch (e: Exception) {
            DebugUtil.d(TAG, "load error: ${e.message}")
            searchRepository.cancelQueryTimeOut()
            LoadResult.Error(e)
        }
    }
}