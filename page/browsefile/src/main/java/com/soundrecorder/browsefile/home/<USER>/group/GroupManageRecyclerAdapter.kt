/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: GroupManageRecyclerAdapter.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/01/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9017070 2025/01/20      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.view.updateMargins
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.state.COUIStateEffectDrawable
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.home.view.group.entity.GroupFactory
import com.soundrecorder.browsefile.home.view.group.entity.GroupItem
import com.soundrecorder.browsefile.home.view.group.entity.GroupRecyclerItemListener
import com.soundrecorder.browsefile.home.view.group.entity.RecordingGroupData
import com.soundrecorder.browsefile.home.view.group.entity.RecordingGroupData.groupColorInDb2PureCover
import com.soundrecorder.browsefile.home.view.group.util.GroupDraggableViewHolder
import com.soundrecorder.browsefile.home.view.group.util.GroupManageUtils
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.db.GroupInfoDbUtil
import java.util.Collections

class GroupManageRecyclerAdapter(
    context: Context,
    val groupRecyclerItemListener: GroupRecyclerItemListener? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "GroupManageRecyclerAdapter"
        private const val CUSTOM_GROUP_START_INDEX: Int = 2
        private const val TAIL_GROUP_NUM: Int = 3 //后续添加私密录音后注意更新。
        private var DARK_ITEM_DROP_BG_COLOR: Int = Color.argb(
            255,
            25,
            25,
            25
        )
        private var DARK_ITEM_BG_COLOR: Int = Color.argb(
            26,
            255,
            255,
            255
        )
        private var ITEM_BG_COLOR: Int = Color.argb(
            255,
            255,
            255,
            255
        )
    }

    private val groupGap: Int =
        context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp16)
    val values = mutableListOf<GroupItem>()
    private var preSelectedPosition: Int = RecyclerView.NO_POSITION

    private var isCheckableState = false

    private var itemTouchHelper: ItemTouchHelper? = null
    private var originItems = mutableListOf<GroupItem>()

    fun refresh(recyclerView: RecyclerView?, items: List<GroupItem>) {
        updateCheckedStatus(values, items)
        values.clear()
        values.addAll(items)
        preSelectedPosition = items.indexOfFirst { it.selected }
        recyclerView?.post { notifyDataSetChanged() }
    }

    fun refreshDeleteEvent(deletedItems: List<GroupItem>) {
        //获取需要删除的GroupItem项在原始列表中的索引的集合
        val needDeletedItemIndex = values.mapIndexed { index, element ->
            index to element
        }.filter { (_, element) -> element in deletedItems }
            .map { (index, _) -> index }

        //单独处理数据对象和界面刷新，防止数组越界。
        values.let { it.removeAll(it.filterIndexed { index, _ -> index in needDeletedItemIndex }) }

        //更新自定义组的样式，。
        val customGroups = values.filter { it.groupInfo?.mGroupType == GroupInfo.INT_DEFAULT_NONE }
        val firstGroupInfo = customGroups.firstOrNull()
        val lastGroupInfo = customGroups.lastOrNull()
        if (firstGroupInfo != null && firstGroupInfo == lastGroupInfo) {
            customGroups.first().cardType = COUICardListHelper.FULL
            notifyItemChanged(values.indexOf(firstGroupInfo))
        } else {
            firstGroupInfo?.cardType = COUICardListHelper.HEAD
            lastGroupInfo?.cardType = COUICardListHelper.TAIL
            notifyItemChanged(values.indexOf(firstGroupInfo))
            notifyItemChanged(values.indexOf(lastGroupInfo))
        }

        needDeletedItemIndex.forEach {
            notifyItemRemoved(it)
        }

        preSelectedPosition = values.indexOfFirst { it.selected }
    }

    fun update(recyclerView: RecyclerView?, groupInfo: GroupInfo) {
        val find = values.find { it.groupInfo?.mUuId == groupInfo.mUuId }
        find?.groupInfo?.mGroupName = groupInfo.mGroupName
        find?.groupInfo?.mGroupColor = groupInfo.mGroupColor
        recyclerView?.post {
            notifyDataSetChanged()
        }
    }

    fun getCurrentValues(): List<GroupItem> {
        return values
    }

    fun setItemTouchHelper(itemTouchHelper: ItemTouchHelper) {
        this.itemTouchHelper = itemTouchHelper
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        when (viewType) {
            GroupItem.VIEW_TYPE_NORMAL -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_group_list_fragment_edit_normal, parent, false)
                return RecordingGroupViewHolder(view)
            }

            GroupItem.VIEW_TYPE_GROUP_TITLE -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_group_fragment_title, parent, false)
                return RecordingGroupTitleViewHolder(view)
            }

            else -> throw IllegalArgumentException("Unsupported view type. $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is RecordingGroupViewHolder -> holder.bind(values[position])
            is RecordingGroupTitleViewHolder -> holder.bind()
            is GroupDraggableViewHolder -> holder.onBindViewHolder(itemTouchHelper)
            else -> DebugUtil.w(TAG, "other onBindViewHolder holder: $holder")
        }
    }

    override fun getItemCount(): Int = values.size

    override fun getItemViewType(position: Int): Int {
        return values[position].viewType
    }

    fun toggleCheckableState(recyclerView: RecyclerView?) {
        isCheckableState = !isCheckableState

        if (isCheckableState) {
            values.forEach { it.checked = false }
        }
        if (recyclerView != null) {
            recyclerView.post { notifyDataSetChanged() }
        } else {
            notifyDataSetChanged()
        }
    }

    fun isCheckableState(): Boolean {
        return isCheckableState
    }

    fun updateSelectedDataAndRefreshUI(curPosition: Int) {
        val prePosition = preSelectedPosition

        if (curPosition == prePosition) {
            groupRecyclerItemListener?.onSelectedChangedListener?.invoke(null) { }
            return
        }

        groupRecyclerItemListener?.onSelectedChangedListener?.invoke(values.getOrNull(curPosition)) {
            notifyItemChanged(curPosition)
            notifyItemChanged(prePosition)

            preSelectedPosition = curPosition
        }
    }

    fun updateCheckedDataAndRefreshUI(curPosition: Int) {
        val data = values.getOrNull(curPosition) ?: return
        if (!GroupFactory.isDefaultGroup(data.groupInfo)) {
            data.checked = !data.checked
        }
        groupRecyclerItemListener?.onCheckedChangedListener?.invoke()
        notifyItemChanged(curPosition)
    }

    fun onItemMove(source: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder) {
        val sourcePosition = source.bindingAdapterPosition
        val targetPosition = target.bindingAdapterPosition
        movePosition = targetPosition

        val sourceItem = values[sourcePosition]
        val targetItem = values[targetPosition]
        val tempCardType = sourceItem.cardType
        sourceItem.cardType = targetItem.cardType
        targetItem.cardType = tempCardType
        onBindViewHolder(source, sourcePosition)
        onBindViewHolder(target, targetPosition)
        Collections.swap(values, sourcePosition, targetPosition)
        notifyItemMoved(sourcePosition, targetPosition)
    }

    private var dragPosition: Int? = null
    private var movePosition: Int? = null
    private var dropPosition: Int? = null

    private fun notifyRecordingGroupDragStart(dragPosition: Int) {
        originItems = values.subList(CUSTOM_GROUP_START_INDEX, values.size - TAIL_GROUP_NUM).toMutableList()
        this.dragPosition = dragPosition
        this.movePosition = dragPosition
    }

    private fun notifyRecordingGroupDragEnd(dropPosition: Int) {
        val items = mutableListOf<GroupItem>()
        if (values.size - TAIL_GROUP_NUM - 1 > CUSTOM_GROUP_START_INDEX) {
            items.addAll(values.subList(CUSTOM_GROUP_START_INDEX, values.size - TAIL_GROUP_NUM))
        }

        if (originItems != items) { // 有顺序改变时才更新数据库的sort。
            groupRecyclerItemListener?.onDragResultListener?.invoke(items)
        }
        this.movePosition = null
    }

    /**
     * 如果是选择录音弹窗样式时，不存在多选状态，所以onCheckedChangedListener应该为空
     */
    private fun isChooseMode(): Boolean {
        return groupRecyclerItemListener?.onCheckedChangedListener == null
    }

    fun setDefaultSelectedGroup(recyclerView: RecyclerView?) {
        values.find { GroupFactory.isAllRecordingGroup(it.groupInfo) }.let {
            if (it != null) {
                it.selected = true
                recyclerView?.post { notifyDataSetChanged() }
            }
        }
    }

    /**
     * 更新新数据的checked状态。
     */
    private fun updateCheckedStatus(
        oldItems: List<GroupItem>,
        newItems: List<GroupItem>
    ) {
        val oldNeedUpdateData =
            oldItems.filter { it.checked && it.groupInfo?.isCustomGroup() == true }
        newItems.forEach { newItem ->
            oldNeedUpdateData.forEach {
                if (it.groupInfo?.mUuId == newItem.groupInfo?.mUuId) {
                    newItem.checked = true
                }
            }
        }
    }

    inner class RecordingGroupViewHolder(itemView: View) : GroupDraggableViewHolder(itemView),
        COUIRecyclerView.ICOUIDividerDecorationInterface {
        private val couiCardListSelectedItemLayout: COUICardListSelectedItemLayout =
            itemView.findViewById(R.id.group_item_root)
        private val ivRecordingGroupIcon: ImageView =
            itemView.findViewById(R.id.recording_group_item_icon)
        private val tvRecordingGroupName: TextView =
            itemView.findViewById(R.id.recording_group_item_name)
        private val tvRecordingGroupSize: TextView =
            itemView.findViewById(R.id.recording_group_item_count)
        private val cbRecordingGroupCheck: CheckBox =
            itemView.findViewById(R.id.recording_group_item_select)

        /* private val ivRecordingGroupEncrypt: ImageView =
             itemView.findViewById(R.id.recording_group_item_encrypt)*/
        private val ivRecordingGroupNext: ImageView =
            itemView.findViewById(R.id.recording_group_item_next)
        private val flRecordingGroupContainer: View =
            itemView.findViewById(R.id.recording_group_item_end_container)

        private val ivRecordingGroupDragView =
            itemView.findViewById<ImageView>(R.id.recording_group_item_drag)

        init {
            itemView.setOnClickListener {
                if (isCheckableState()) {
                    updateCheckedDataAndRefreshUI(bindingAdapterPosition)
                } else {
                    updateSelectedDataAndRefreshUI(bindingAdapterPosition)
                }
            }
            setItemLongClickListener()
        }

        override fun onItemSelected() {
            super.onItemSelected()
            groupRecyclerItemListener?.onItemMoveListener?.invoke(false)
            notifyRecordingGroupDragStart(bindingAdapterPosition)
        }

        override fun onItemDrop() {
            super.onItemDrop()
            groupRecyclerItemListener?.onItemMoveListener?.invoke(true)
            notifyRecordingGroupDragEnd(bindingAdapterPosition)
            //控件bug，只有通过Color.argb设置色值才能成功。直接应用控件颜色会变蓝。
            this.couiCardListSelectedItemLayout.refreshCardBg(
                if (NightModeUtil.isNightMode(itemView.context)) {
                    DARK_ITEM_BG_COLOR
                } else {
                    ITEM_BG_COLOR
                }
            )
        }

        override fun getDragView(): View? {
            return ivRecordingGroupDragView
        }

        override fun isDraggable(): Boolean {
            val item = values.getOrNull(bindingAdapterPosition)?.groupInfo
            if (GroupFactory.isEmbedGroup(item)) {
                return false
            }
            return super.isDraggable()
        }

        fun bind(data: GroupItem) {
            refreshBackground(data)
            refreshGroupIcon(data)
            refreshRecordingGroupName(data)
            refreshRecordingGroupCount(data)
            refreshRecordingGroupCheck(data)
            refreshRecordingGroupSelect(data)
            refreshDragView(data)
            refreshRecordingGroupNext(data)
        }

        private fun setItemLongClickListener() {
            if (groupRecyclerItemListener?.onItemLongClickListener != null) {
                itemView.setOnLongClickListener {
                    if (!isCheckableState()) {
                        DebugUtil.d(TAG, "enter checkable state: $this")
                        // 提前获取position信息，避免列表更新中位置信息获取不对
                        val position = bindingAdapterPosition
                        // 禁止当前长按item复用，避免列表刷新时复用至其它位置，显示异常按压效果
                        setIsRecyclable(false)
                        groupRecyclerItemListener.onItemLongClickListener.invoke(position)
                    } else {
                        itemTouchHelper?.startDrag(this)
                        if (NightModeUtil.isNightMode(itemView.context)) {
                            this.couiCardListSelectedItemLayout.refreshCardBg(
                                DARK_ITEM_DROP_BG_COLOR
                            )
                        }
                    }
                    return@setOnLongClickListener true
                }
            }
        }

        private fun refreshBackground(data: GroupItem) {
            val root = itemView
            val cardType = data.cardType
            COUICardListHelper.setItemCardBackground(root, cardType)
            if (cardType == COUICardListHelper.TAIL || cardType == COUICardListHelper.FULL) {
                val params = root.layoutParams as? RecyclerView.LayoutParams
                params?.updateMargins(bottom = groupGap)
            } else {
                val params = root.layoutParams as? RecyclerView.LayoutParams
                params?.updateMargins(bottom = 0)
            }

            root.isEnabled = if (isCheckableState()) data.enableInChecked else true
        }

        private fun refreshGroupIcon(data: GroupItem) {
            if (GroupFactory.isAllRecordingGroup(data.groupInfo)) {
                ivRecordingGroupIcon.setImageResource(
                    if (isCheckableState) {
                        R.drawable.ic_recording_group_all_disable
                    } else {
                        R.drawable.ic_recording_group_all
                    }
                )
            } else if (GroupFactory.isCallRecordingGroup(data.groupInfo)) {
                ivRecordingGroupIcon.setImageResource(
                    if (isCheckableState) {
                        R.drawable.ic_recording_group_call_disable
                    } else {
                        R.drawable.ic_recording_group_call
                    }
                )
            } else if (GroupFactory.isCommonGroup(data.groupInfo)) {
                ivRecordingGroupIcon.setImageResource(
                    if (isCheckableState) {
                        R.drawable.ic_recording_group_normal_disable
                    } else {
                        R.drawable.ic_recording_group_normal
                    }
                )
            } /*else if (GroupFactory.isPrivateGroup(data.groupInfo)) {
                ivRecordingGroupIcon.setImageResource(R.drawable.ic_group_encrypt)
            }*/ else if (GroupFactory.isRecentDeleteGroup(data.groupInfo)) {
                ivRecordingGroupIcon.setImageResource(
                    if (isCheckableState) {
                        R.drawable.menu_ic_delete_normal_disable
                    } else {
                        R.drawable.menu_ic_delete_normal
                    }
                )
            } else {
                val id = GroupManageUtils.getResIdByResName(
                    itemView.context,
                    (data.groupInfo?.mGroupColor?.let { groupColorInDb2PureCover(it) }
                        ?: RecordingGroupData.getDefaultPureCover()).toString()
                )
                ivRecordingGroupIcon.setImageResource(if (id > 0) id else R.drawable.ic_group_cover_red)
            }
            ivRecordingGroupIcon.isEnabled = if (isCheckableState()) data.enableInChecked else true
        }

        private fun refreshRecordingGroupName(data: GroupItem) {
            tvRecordingGroupName.text = when (data.groupInfo?.mUuId) {
                GroupInfoDbUtil.DEFAULT_ALL_UUID -> itemView.context.getString(com.soundrecorder.common.R.string.all_the_recordings)
                GroupInfoDbUtil.DEFAULT_CALLING_UUID -> itemView.context.getString(com.soundrecorder.common.R.string.incall_recording_tab)
                GroupInfoDbUtil.DEFAULT_COMMON_UUID -> itemView.context.getString(com.soundrecorder.common.R.string.normal_recording_tab)
                GroupInfoDbUtil.DEFAULT_RECENTLY_DELETE_UUID -> itemView.context.getString(com.soundrecorder.common.R.string.recycle_recently_deleted)
                else -> data.groupInfo?.mGroupName
            }
            tvRecordingGroupName.isEnabled = if (isCheckableState()) data.enableInChecked else true
            var groupNameColor = com.support.appcompat.R.color.coui_color_label_primary
            if (GroupFactory.isDefaultGroup(data.groupInfo)) {
                groupNameColor =
                    if (isCheckableState) {
                        com.support.appcompat.R.color.coui_color_label_tertiary
                    } else {
                        com.support.appcompat.R.color.coui_color_label_primary
                    }
            }
            tvRecordingGroupName.setTextColor(
                ContextCompat.getColor(
                    itemView.context,
                    groupNameColor
                )
            )
        }

        @SuppressLint("SetTextI18n")
        private fun refreshRecordingGroupCount(data: GroupItem) {
            tvRecordingGroupSize.text = "${data.recordingCount}"
            tvRecordingGroupSize.visibility = if (!data.encrypted) View.VISIBLE else View.GONE
            tvRecordingGroupSize.isEnabled = if (isCheckableState()) data.enableInChecked else true

            val textColor = if (GroupFactory.isDefaultGroup(data.groupInfo) && isCheckableState) {
                com.support.appcompat.R.color.coui_color_label_tertiary
            } else {
                com.support.appcompat.R.color.coui_color_label_secondary
            }
            tvRecordingGroupSize.setTextColor(ContextCompat.getColor(itemView.context, textColor))
        }

        private fun refreshRecordingGroupCheck(data: GroupItem) {
            cbRecordingGroupCheck.visibility =
                if (isCheckableState() && data.enableInChecked && !GroupFactory.isAllRecordingGroup(
                        data.groupInfo
                    )
                ) View.VISIBLE else View.GONE
            cbRecordingGroupCheck.isChecked = data.checked
        }

        private fun refreshRecordingGroupSelect(data: GroupItem) {
            val root = itemView
            if (root is COUICardListSelectedItemLayout) {
                val background: Drawable = root.background
                if (background is COUIStateEffectDrawable) {
                    if (!isCheckableState() && data.selected) {
                        root.setIsSelected(true)
                    } else {
                        root.setIsSelected(false)
                    }
                }
            }
        }

        private fun refreshDragView(data: GroupItem) {
            ivRecordingGroupDragView.isVisible =
                isCheckableState() && !GroupFactory.isEmbedGroup(data.groupInfo)
        }

        private fun refreshRecordingGroupNext(data: GroupItem) {
            ivRecordingGroupNext.visibility =
                if ((isCheckableState() || isChooseMode()) && !GroupFactory.isAllRecordingGroup(data.groupInfo)) View.GONE else View.VISIBLE
        }

        override fun drawDivider(): Boolean {
            if (bindingAdapterPosition == movePosition || bindingAdapterPosition == ((movePosition
                    ?: 0) - 1)
            ) {
                return false
            }

            if (itemView is COUICardListSelectedItemLayout) {
                val cardType = values.getOrNull(bindingAdapterPosition)?.cardType
                return cardType == COUICardListHelper.HEAD || cardType == COUICardListHelper.MIDDLE
            }
            return false
        }

        override fun getDividerStartAlignView(): View {
            return tvRecordingGroupName
        }

        override fun getDividerEndAlignView(): View {
            return flRecordingGroupContainer
        }
    }

    inner class RecordingGroupTitleViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView),
        COUIRecyclerView.ICOUIDividerDecorationInterface {

        private val btnNew = itemView.findViewById<TextView>(R.id.create_group_item)

        init {
            COUITextViewCompatUtil.setPressRippleDrawable(btnNew)
            btnNew.setOnClickListener(groupRecyclerItemListener?.onCreateNewGroupClickListener)
        }

        fun bind() {
            btnNew.visibility = if (isCheckableState()) View.INVISIBLE else View.VISIBLE
        }

        override fun drawDivider(): Boolean {
            return false
        }
    }
}