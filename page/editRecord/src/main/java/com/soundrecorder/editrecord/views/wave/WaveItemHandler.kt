/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RegionSelectListener
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: <EMAIL>
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.views.wave

import android.os.Handler
import android.os.Looper
import android.os.Message
import com.soundrecorder.editrecord.EditConstant
import com.soundrecorder.editrecord.R
import java.lang.ref.WeakReference

class WaveItemHandler(itemView: EditWaveItemView) : Handler(Looper.getMainLooper()) {
    private val reference: WeakReference<EditWaveItemView> = WeakReference(itemView)

    companion object {
        private const val FLOAT_DEL_RATIO = 0.1F
    }

    override fun handleMessage(msg: Message) {
        when (msg.what) {
            EditWaveItemView.ADD -> {
                reference.get()?.let {
                    if (EditWaveItemView.sDelRadio < it.resources.getDimension(com.soundrecorder.common.R.dimen.dp1) / EditWaveItemView.FLOAT_TWO) {
                        EditWaveItemView.sDelRadio += FLOAT_DEL_RATIO
                        msg.what = EditWaveItemView.ADD
                        sendEmptyMessageDelayed(msg.what, EditConstant.DURATION_60)
                    }
                    it.postInvalidateOnAnimation()
                }
            }
            EditWaveItemView.RESET -> {
                reference.get()?.let {
                    if (EditWaveItemView.sDelRadio > it.resources.getDimension(com.soundrecorder.common.R.dimen.dp1) / EditWaveItemView.FLOAT_TWO) {
                        EditWaveItemView.sDelRadio -= FLOAT_DEL_RATIO
                        msg.what = EditWaveItemView.RESET
                        sendEmptyMessageDelayed(msg.what, EditConstant.DURATION_60)
                    }
                    it.postInvalidateOnAnimation()
                }
            }
        }
    }
}