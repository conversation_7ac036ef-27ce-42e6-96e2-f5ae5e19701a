/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: GloblePreViewBarTest
 * Description:
 * Version: 1.0
 * Date: 2023/5/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/5/18 1.0 create
 */

package com.soundrecorder.editrecord.views.preview

import android.content.Context
import android.graphics.Canvas
import android.os.Build
import android.view.MotionEvent
import android.view.View
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class GloblePreViewBarTest {
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun should_addView() {
        val preViewBar = GloblePreViewBar(mContext)
        Assert.assertNotNull(Whitebox.getInternalState(preViewBar, "mAmplitudeView"))
        Assert.assertTrue(preViewBar.childCount > 0)
    }

    @Test
    fun should_addDragBar_when_setAmplitudes() {
        val preViewBar = GloblePreViewBar(mContext)
        var dragBar = Whitebox.getInternalState<View>(preViewBar, "mDragBar")
        Assert.assertNotNull(dragBar)
        Assert.assertNull(dragBar.parent)

        val ampList = mutableListOf<Int>().apply { add(1) }
        preViewBar.amplitudes = ampList
        dragBar = Whitebox.getInternalState<View>(preViewBar, "mDragBar")
        Assert.assertNotNull(dragBar.parent)
        Assert.assertNotNull(preViewBar.amplitudes)

        preViewBar.amplitudes = mutableListOf<Int>().apply { add(1) }
        preViewBar.resetStatus()
        Assert.assertTrue(preViewBar.amplitudes.isEmpty())
    }

    @Test
    fun addFixWidth() {
        val preViewBar = GloblePreViewBar(mContext)
        Assert.assertEquals(0, preViewBar.addFixWidth())

        preViewBar.amplitudes = mutableListOf<Int>().apply { add(1) }
        Assert.assertNotEquals(0, preViewBar.addFixWidth())
    }

    @Test
    fun getTotalAmpWidth() {
        val preViewBar = GloblePreViewBar(mContext)
        Assert.assertEquals(0, preViewBar.totalAmpWidth)

        preViewBar.amplitudes = mutableListOf<Int>()
        Assert.assertEquals(0, preViewBar.totalAmpWidth)

        preViewBar.amplitudes = mutableListOf<Int>().apply { add(1) }
        Assert.assertEquals(0, preViewBar.totalAmpWidth)
    }

    @Test
    fun getTotalTime() {
        val preViewBar = GloblePreViewBar(mContext)
        preViewBar.totalTime = 10
        Assert.assertEquals(10, preViewBar.totalTime)

        preViewBar.setSelectTime(1)
        Assert.assertEquals(1, Whitebox.getInternalState(preViewBar, "mPreTime"))

        preViewBar.resetPreTime()
        Assert.assertEquals(-1, Whitebox.getInternalState(preViewBar, "mPreTime"))
    }

    @Test
    fun refreshPoint() {
        val preViewBar = GloblePreViewBar(mContext)
        Whitebox.invokeMethod<Unit>(preViewBar, "refreshPoint", 10L)
        Assert.assertEquals(0, Whitebox.getInternalState(preViewBar, "mPointTime"))

        Whitebox.setInternalState(preViewBar, "mPreTime", 5)
        Whitebox.invokeMethod<Unit>(preViewBar, "refreshPoint", 10L)
        Assert.assertEquals(5, Whitebox.getInternalState(preViewBar, "mPointTime"))

        Whitebox.invokeMethod<Unit>(preViewBar, "refreshPoint", 1L)
    }

    @Test
    fun onDraw() {
        val preViewBar = GloblePreViewBar(mContext)
        preViewBar.onDraw(Canvas())

        preViewBar.totalTime = 1000
        preViewBar.amplitudes = mutableListOf<Int>().apply { add(1) }
        preViewBar.onDraw(Canvas())
    }

    @Test
    fun onTouchEvent() {
        val preViewBar = GloblePreViewBar(mContext)
        var motionEvent = MotionEvent.obtain(
            System.currentTimeMillis(), 200, MotionEvent.ACTION_DOWN, 200f, 200f, 0)
        preViewBar.onTouchEvent(motionEvent)

        motionEvent = MotionEvent.obtain(
            System.currentTimeMillis(), 200, MotionEvent.ACTION_MOVE, 200f, 200f, 0)
        preViewBar.onTouchEvent(motionEvent)

        motionEvent = MotionEvent.obtain(
            System.currentTimeMillis(), 200, MotionEvent.ACTION_CANCEL, 200f, 200f, 0)
        preViewBar.onTouchEvent(motionEvent)
    }

    @Test
    fun correctPointTime() {
        val preViewBar = GloblePreViewBar(mContext)
        preViewBar.totalTime = 10000
        preViewBar.setCutStartTime(2000)
        preViewBar.setCutEndTime(7000)

        Whitebox.invokeMethod<Unit>(preViewBar, "correctPointTime", -1L)
        Assert.assertEquals(2000, Whitebox.getInternalState(preViewBar, "mPointTime"))

        Whitebox.invokeMethod<Unit>(preViewBar, "correctPointTime", 1000L)
        Assert.assertEquals(2000, Whitebox.getInternalState(preViewBar, "mPointTime"))

        Whitebox.invokeMethod<Unit>(preViewBar, "correctPointTime", 8000L)
        Assert.assertEquals(7000, Whitebox.getInternalState(preViewBar, "mPointTime"))
    }

    @Test
    fun setMiddleLineFollowDragBar() {
        val preViewBar = GloblePreViewBar(mContext)
        Assert.assertFalse(Whitebox.getInternalState(preViewBar, "mMiddleLineFollowDragBar"))
        preViewBar.setMiddleLineFollowDragBar(true)
        Assert.assertTrue(Whitebox.getInternalState(preViewBar, "mMiddleLineFollowDragBar"))
    }
}