package com.soundrecorder.playback.audio.setting;

import android.content.Context;
import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.playback.PlaybackActivity;
import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;

import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class,
        ShadowFeatureOption.class, ShadowCOUIVersionUtil.class})
public class PlaySettingDialogFragmentTest {
    private Context mContext;
    private ActivityController<PlaybackActivity> mController;
    private PlaybackActivity mActivity;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        mController = Robolectric.buildActivity(PlaybackActivity.class);
        mActivity = mController.create().get();
    }

    @After
    public void tearDown() {
        mContext = null;
        mController = null;
        mActivity = null;
    }

    @Test
    public void should_returnNotnull_when_initView() throws Exception {
        PlaySettingDialogFragment playSettingDialogFragment = new PlaySettingDialogFragment();
        Assert.assertNotNull(Whitebox.getInternalState(playSettingDialogFragment, "mPreferenceDialog"));
    }
}
