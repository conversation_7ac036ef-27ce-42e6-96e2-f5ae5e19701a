/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert

import android.app.Application
import android.content.Context
import android.os.AsyncTask
import android.text.TextUtils
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OS12FeatureUtil.isSuperSoundRecorderEpicEffective
import com.soundrecorder.common.buryingpoint.ConvertStaticsUtil
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.KeyWordDbUtils
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.ConvertDbUtil.SHOW_SWITH_TRUE
import com.soundrecorder.convertservice.convert.ConvertTaskThreadManager
import com.soundrecorder.convertservice.convert.NewConvertResultUtil
import com.soundrecorder.playback.MarkReadReadyCallback
import com.soundrecorder.playback.cloudconfig.CloudConfigUtils
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.playback.convert.search.ConvertSearchBean
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
import com.soundrecorder.playback.newconvert.search.ConvertSearchHelper
import com.soundrecorder.playback.newconvert.search.anim.ConvertSearchAnim
import com.soundrecorder.playback.newconvert.ui.ConvertLoadedCallback
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.playback.newconvert.view.TextImageMixLayout.Companion.SPACE
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

class PlaybackConvertViewModel(application: Application) : AndroidViewModel(application),
    MarkReadReadyCallback {

    companion object {
        const val TAG = "PlaybackConvertModelWrapper"
        const val CONVERT_STATUS_INIT = 1
        const val CONVERT_STATUS_PROGRESS = 2
        const val CONVERT_STATUS_COMPLETE = 3
        const val CONVERT_STATUS_CANCEL = 4
        const val CONVERT_STATUS_USERTIMEOUT = 5
        const val CONVERT_STATUS_SUMMARY_NONE_COMPLETE = 6
    }

    var viewPagerScrollEnable = MutableLiveData<Boolean?>()
    var originContent: String = ""
    var convertContentData: List<ConvertContentItem>? = null
    /*记录所有数据里的文本item、时间item*/
    var mTextItems: MutableList<Pair<Int, ConvertContentItem.TextItemMetaData>>? = null
    var timeItems: MutableList<Pair<Int, ConvertContentItem.TimerDividerMetaData>>? = null
    var markLoaded: AtomicBoolean = AtomicBoolean(false)
    /*被筛选的讲话人列表*/
    var selectSpeakerList: List<String>? = null

    var contentInRenameSpeakerDialog = ""
    var isModifyAllInRenameSpeakerDialog = false
    var isRenameSpeakerDialogShowing = false
    var isUserTimeOutDialogShowing = false
    var isExportDialogShowing = false
    var isExportDialogSupportFollow = false
    var isExportTextDialogShowing = false
    var isExportTextDialogSupportFollow = false

    var isSpeakerRoleShowing = MutableLiveData<Boolean>(false)

    var mediaRecordId: Long = -1
        private set
    var mConvertLoadedCallback: ConvertLoadedCallback? = null
    var mFileSize: Long? = null
    var mFileDuration: Long? = null
    var mFileFormat: String? = null

    var mServerPlanCode = 0
    var mConvertProgress: MutableLiveData<Int> = MutableLiveData()
    var mConvertStatus: MutableLiveData<Int> = MutableLiveData()
    var mNeedTransConvert = false
    /**
     * mShowSwitchInConvertModel should be false before os12.1
     */
    var mShowSwitchInConvertModel: MutableLiveData<Boolean> = MutableLiveData()
    var mOriginalRoleNumber: MutableLiveData<Int> = MutableLiveData()
    var mUserConvertTimePerDay: String = ""

    var mHasUserDraggedText: MutableLiveData<Boolean> = MutableLiveData(false)

    var mRecord: Record? = null

    var mContentBackDirection: MutableLiveData<Boolean> = MutableLiveData()

    var keyWords: MutableLiveData<List<String>> = MutableLiveData()

    // 提取关键词按钮的状态
    var extractState: Int = KeyWordChipGroup.DEFAULT_STATE
    var searchAnimEnd: MutableLiveData<Boolean> = MutableLiveData()

    // 转文本搜索
    var mIsInConvertSearch = MutableLiveData(false)
    var mConvertSearchValue: String? = null
    val mConvertSearchResult = MutableLiveData<List<ConvertSearchBean>>()
    var searchHelper: ConvertSearchHelper? = null

    // 当前文本搜索跳转的位置
    var currentPos = 0

    //上一次文本搜索跳转的位置
    var lastPos = 0

    // 是否开启自动搜索
    var autoSearch = false

    // 点击关键词标签的次数
    var clickChipCount = 0

    // 点击文本搜索按钮的次数
    var clickConvertSearchCount = 0

    /**是否需要检测讲话人 首次进入页面显示讲话人*/
    var isCheckSpeakerRole: Boolean = false
    /**
     * 页面滚动的位置
     * first:第一个可见的item
     * second:可见item滚动的距离
     */
    var visibleItemLocation = MutableLiveData<Pair<Int, Int>>()

    /**
     * 获取当前滚动的位置
     * @param tag 标签，用作日志打印
     * @param layoutManager RecyclerView的LayoutManager,用于获取第一个可见item和top值
     */
    fun saveScrollPosition(tag: String, layoutManager: LinearLayoutManager) {
        val visibleItemPosition = layoutManager.findFirstVisibleItemPosition()
        if (RecyclerView.NO_POSITION == visibleItemPosition) {
            DebugUtil.e(TAG, "$tag saveScrollPosition don't find visible item")
            return
        }
        val view = layoutManager.findViewByPosition(visibleItemPosition)
        val scrollY = view?.top ?: 0
        val pair = Pair(visibleItemPosition, scrollY)
        DebugUtil.d(TAG, "$tag saveScrollPosition position:$pair")
        visibleItemLocation.value = pair
    }

    /**
     * 恢复当前滚动的位置
     */
    fun restoreScrollPosition(layoutManager: LinearLayoutManager) {
        val location = visibleItemLocation.value
        DebugUtil.d(TAG, "restoreScrollPosition position:$location")
        location?.let {
            layoutManager.scrollToPositionWithOffset(it.first, it.second)
        }
    }

    /**
     * 判断提取关键词列表是否在当前屏幕
     * 目前只用于转文本界面和转文本搜索界面
     */
    fun isHeaderInScreen(): Boolean {
        val location = visibleItemLocation.value
        return location?.first == 0
    }

    /**
     * 统计埋点
     */
    private fun staticsEvent() {
        DebugUtil.i(TAG, "staticsEvent 文本搜索:$clickConvertSearchCount 关键词标签:$clickChipCount")
        if (clickConvertSearchCount > 0) { //点击文本搜索的埋点
            ConvertStaticsUtil.addInConvertSearchEvent(clickConvertSearchCount)
        }
        if (clickChipCount > 0) { //点击关键词标签的埋点
            ConvertStaticsUtil.clickKeyWordChipEvent(clickChipCount)
        }
    }

    fun setMediaRecordId(recordId: Long) {
        mediaRecordId = recordId
        if (recordId > 0) {
            getInitIsSpeakerRoleShowing(recordId)
        }
    }

    private fun getInitIsSpeakerRoleShowing(recordId: Long) {
        if (isCheckSpeakerRole) {
            viewModelScope.launch(Dispatchers.IO) {
                val convertRecord = ConvertDbUtil.selectByRecordId(recordId)
                DebugUtil.i(TAG, "getInitIsSpeakerRoleShowing speakerRoleIsShowing = ${convertRecord?.speakerRoleIsShowing}")
                val result = convertRecord?.speakerRoleIsShowing == ConvertDbUtil.SPEAKER_ROLE_ISSHOWING_TRUE
                DebugUtil.i(TAG, "getInitIsSpeakerRoleShowing result = $result")
                isSpeakerRoleShowing.postValue(result)
            }
        } else {
            isCheckSpeakerRole = true
            isSpeakerRoleShowing.value = true
            /*
            自检问题修复：大小屏切换或其他情况导致重建时，因为Record id 未改变，是否显示讲话人的状态值会直接从数据库里查询。
            所以这里需要把isSpeakerRoleShowing.value的值存入数据库，否则重建后不会显示讲话人
             */
            updateDbIsSpeakerRoleShowing(true)
        }
    }

    fun swithSpeakerRoleState() {
        viewModelScope.launch(Dispatchers.IO) {
            val targetStatus = !(isSpeakerRoleShowing.value ?: false)
            DebugUtil.i(TAG, "swithSpeakerRoleState targetStatus = $targetStatus")
            updateDbIsSpeakerRoleShowing(targetStatus)
            isSpeakerRoleShowing.postValue(targetStatus)
            ConvertStaticsUtil.addClickSpeakerSwitchEventOnConvert(targetStatus)
        }
    }

    private fun updateDbIsSpeakerRoleShowing(isSpeakerRoleShowing: Boolean) {
        var isShowing = ConvertDbUtil.SPEAKER_ROLE_ISSHOWING_FALSE
        if (isSpeakerRoleShowing) {
            isShowing = ConvertDbUtil.SPEAKER_ROLE_ISSHOWING_TRUE
        }
        DebugUtil.i(TAG, "updateDbIsSpeakerRoleShowing isShowing=$isShowing")
        ConvertDbUtil.updateSpeakerRoleIsShowing(mediaRecordId, isShowing)
    }

    fun executeAsyncCheckCompletedConvert() {
        CheckConvertTask().executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR)
    }

    data class InitConvertResult(
        var convertRecord: ConvertRecord? = null,
        var convertFileContents: ArrayList<ConvertContentItem>? = null,
        var keyWords: List<String> = emptyList(),
        var noteSummaryExit: Boolean = false
    )


    inner class CheckConvertTask : AsyncTask<Void?, Void?, InitConvertResult?>() {
        override fun doInBackground(vararg params: Void?): InitConvertResult? {
            val context = BaseApplication.getAppContext()
            if (PermissionUtils.isStatementConvertGranted(context) && PermissionUtils.isNetWorkGranted(context)) {
                CloudConfigUtils.updateConvertConfig()
            }
            mRecord = MediaDBUtils.queryRecordById(mediaRecordId) ?: return null
            mFileSize = mRecord?.mFileSize
            DebugUtil.i(TAG, "CheckConvertTask start. record:$mRecord")
            if (PermissionUtils.hasReadAudioPermission()) {
                ConvertDbUtil.updateRecordIdByMediaPath(mRecord?.data, mediaRecordId)
            }
            val convertRecord = ConvertDbUtil.selectByRecordId(mediaRecordId)
            if (convertRecord == null) {
                DebugUtil.i(TAG, "MediaMetadataRetriever start")
                val mLocalUri = MediaDBUtils.genUri(mediaRecordId)
                val pairFormatAndDuration = MediaDBUtils.getFileFormatAndDurationFromUri(mLocalUri)
                mFileFormat = pairFormatAndDuration.first
                mFileDuration = pairFormatAndDuration.second
                val isNoteSummaryExit = NoteDbUtils.isNoteSummaryExist(context, mediaRecordId)
                val result = if (isNoteSummaryExit) {
                    InitConvertResult(noteSummaryExit = true)
                } else {
                    null
                }
                DebugUtil.i(TAG, "MediaMetadataRetriever end. mFileFormat:$mFileFormat, mFileDuration:$mFileDuration")
                return result
            }
            DebugUtil.i(TAG, "CheckConvertTask check progress.convertRecord = ${convertRecord.recordId}, ${convertRecord.isOShareFile}")
            val convertFileName = getConvertFileName(convertRecord, context)
            val convertContentItems: ArrayList<ConvertContentItem>? = if (TextUtils.isEmpty(convertFileName)) {
                null
            } else {
                runCatching {
                    // 如果转文本文件是OShare目录下的文件，采用对应文本解析规则
                    if (convertRecord.isOShareFile) {
                        ConvertToUtils.readOShareConvertContent(convertRecord.convertTextfilePath, convertRecord.serverPlanCode)
                    } else {
                        ConvertToUtils.readConvertContent(context, convertFileName, convertRecord.serverPlanCode)
                    }
                }.onFailure {
                    DebugUtil.e(TAG, "readConvertContent error.${it.message}")
                }.getOrNull()
            }
            // 从数据库中查询高频词
            val keyWords = KeyWordDbUtils.queryKeyWords(mediaRecordId).map { it.name }
            waitUntilMarkLoadComplete()
            //查询是否有摘要
            val isNoteSummaryExit = NoteDbUtils.isNoteSummaryExist(context, mediaRecordId)
            return InitConvertResult(convertRecord, convertContentItems, keyWords, isNoteSummaryExit)
        }

        override fun onPostExecute(result: InitConvertResult?) {
            val convertRecord = result?.convertRecord
            keyWords.value = result?.keyWords ?: emptyList()
            val speakerRoleOriginalNumber = convertRecord?.speakerRoleOriginalNumber
            val isCompleteConvert = ConvertDbUtil.checkAlreadyConvertComplete(convertRecord)
            val isConvertCurrent = ConvertTaskThreadManager.checkIsTaskRunning(mediaRecordId)
            val isNoteSummaryExist = result?.noteSummaryExit ?: false
            DebugUtil.i(TAG, "CheckConvertTask isCompleteConvert: $isCompleteConvert, " +
                    "isConvertCurrent:$isConvertCurrent, " +
                    "speakerRoleOriginalNumber: $speakerRoleOriginalNumber," +
                    "isNoteSummaryExist: $isNoteSummaryExist")
            if (isCompleteConvert) {
                convertContentData = result?.convertFileContents ?: mutableListOf()
                if (isSuperSoundRecorderEpicEffective()) {
                    mShowSwitchInConvertModel.value = convertRecord?.canShowSpeakerRole == SHOW_SWITH_TRUE
                } else {
                    mShowSwitchInConvertModel.value = false
                }
                mOriginalRoleNumber.value = speakerRoleOriginalNumber ?: 0
                mConvertStatus.value = CONVERT_STATUS_COMPLETE
            } else {
                when {
                    isConvertCurrent -> mConvertLoadedCallback?.onLoadCurrentConvert()
                    isNoteSummaryExist -> mConvertStatus.value = CONVERT_STATUS_SUMMARY_NONE_COMPLETE
                    else -> mConvertStatus.value = CONVERT_STATUS_INIT
                }
            }
        }
    }


    private fun getConvertFileName(convertRecord: ConvertRecord?, context: Context): String? {
        val convertFilePath: String? = convertRecord?.convertTextfilePath
        var convertFileName: String? = null
        if (!TextUtils.isEmpty(convertFilePath)) {
            val savePathDir = NewConvertResultUtil.getConvertSavePath(context) + File.separator
            convertFileName = convertFilePath?.replace(savePathDir, "")
        }
        DebugUtil.i(TAG, "getConvertFileName, convertFileName:$convertFileName convertFilePath = $convertFilePath")
        return convertFileName
    }


    override fun onCleared() {
        DebugUtil.i(TAG, "onClear")
        super.onCleared()
        staticsEvent()
        mConvertLoadedCallback = null
        ConvertSearchAnim.reset()
    }

    /**
     *  这个函数输入为当前的searchBean，输出时当前searchBean对应的子分段中输出Pair，
     *  Pair中的String是当前子分段落内UI上实际显示的UI的string（包含文本标记中的空格）
     *  Pair中的Int是档期啊子分段落中UI上对应的的关键词开始的UI上的string的开始Index
     */
    fun rectifyStartSeqForTextMetaDataItem(searchBean: ConvertSearchBean?): Pair<String, Int> {
        if (searchBean == null) {
            return Pair("", 0)
        }
        val itemItemIndex = searchBean.textItemIndex
        val keyWordIndex = searchBean.keyWordIndex
        val keyWord = searchBean.keyWord
        val realTextContent = StringBuilder("")
        var realUIKeyWordIndex = 0
        mTextItems?.let {
            var finish = false
            val currentTextItem =
                it.find { item: Pair<Int, ConvertContentItem.TextItemMetaData> -> item.first == itemItemIndex }
            if (currentTextItem != null) {
                for ((_, sentence) in currentTextItem.second.textParagraph?.withIndex()!!) {
                    if (sentence.onlyHasSimpleMark && !finish) {
                        realUIKeyWordIndex += SPACE.length
                        realTextContent.append(SPACE)
                    }
                    if ((keyWordIndex >= sentence.startCharSeq) && (keyWordIndex + keyWord.length <= sentence.endCharSeq)) {
                        finish = true
                        val diffFromCurrentSentence = keyWordIndex - sentence.startCharSeq
                        realUIKeyWordIndex += diffFromCurrentSentence
                    } else if (!finish) {
                        realUIKeyWordIndex += sentence.text.length
                    }
                    realTextContent.append(sentence.text)
                }
            }
        }
        return Pair(realTextContent.toString(), realUIKeyWordIndex)
    }


    override fun onMarkReadReady(marks: List<MarkDataBean>?) {
        DebugUtil.i(TAG, "onMarkReadReady")
        markLoaded.set(true)
    }

    private fun waitUntilMarkLoadComplete() {
        DebugUtil.i(TAG, "start wait mark load complete")
        while (true) {
            if (markLoaded.get()) {
                break
            }
        }
        DebugUtil.i(TAG, "end wait mark load complete")
    }

    fun updateConvertStatus(state: Int) {
        if (mConvertStatus.value != state) {
            mConvertStatus.postValueSafe(state)
        }
    }
}