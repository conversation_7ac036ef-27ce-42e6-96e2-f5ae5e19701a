/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ConvertSupportAction
 * Description:
 * Version: 1.0
 * Date: 2025/3/12
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/12 1.0 create
 */

package com.soundrecorder.modulerouter.convertService

interface ConvertSupportAction {
    fun isSupportConvert(fromMainProcess: Boolean): Boolean
    fun getConvertSupportType(fromMainProcess: Boolean): Int
}