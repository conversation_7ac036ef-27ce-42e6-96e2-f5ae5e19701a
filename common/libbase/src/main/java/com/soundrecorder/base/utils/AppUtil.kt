/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AppUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.provider.Settings
import android.text.TextUtils
import com.soundrecorder.base.BaseApplication

@Suppress("TooGenericExceptionCaught")
object AppUtil {
    const val IS_BREATH_MODE = "op_breath_mode_status"
    const val BREATH_MODE_WHITE_LIST = "light_zen_white_string"
    private const val TAG = "AppUtil"

    @JvmStatic
    fun getAppVersion(): String {
        return getAppVersion(BaseApplication.getAppContext().packageName)
    }

    /**
     *
     * @param packageName
     * @return 13.0.1
     */
    @JvmStatic
    fun getAppVersion(packageName: String?): String {
        var appVersionName = ""
        try {
            val context: Context = BaseApplication.getAppContext()
            val packageManager = context.packageManager
            if (packageManager != null) {
                val packageInfo = packageManager.getPackageInfo(
                    packageName!!, PackageManager.GET_META_DATA
                )
                appVersionName = packageInfo.versionName ?: ""
                DebugUtil.i(TAG, "packageInfo.appVersion->$appVersionName")
            }
        } catch (e: PackageManager.NameNotFoundException) {
            DebugUtil.e(TAG, e.message)
        }
        return appVersionName
    }

    /**
     * 获取当前app version name
     * @return  13.0.1.123456_15098
     */
    @JvmStatic
    fun getAppVersionName(): CharSequence? {
        var appVersionName: CharSequence? = ""
        try {
            val context: Context = BaseApplication.getAppContext()
            val packageInfo = context.packageManager
                .getPackageInfo(context.packageName, PackageManager.GET_META_DATA)
            val metaData = packageInfo.applicationInfo?.metaData
            if (metaData != null) {
                val versionCommit = metaData["versionCommit"]
                val versionDate = metaData["versionDate"]
                if (versionCommit != null && versionDate != null) {
                    appVersionName = TextUtils.concat(
                        fixString(packageInfo.versionName),
                        "_",
                        fixString(versionCommit.toString()),
                        "_",
                        fixString(versionDate.toString())
                    )
                    DebugUtil.i(TAG, "packageInfo.versionName->" + packageInfo.versionName)
                    DebugUtil.i(TAG, "versionCommit.toString()->$versionCommit")
                    DebugUtil.i(TAG, "versionDate.toString()->$versionDate")
                }
            }
        } catch (e: PackageManager.NameNotFoundException) {
            DebugUtil.e(TAG, e.message)
        }
        return appVersionName
    }

    @JvmStatic
    fun getAppVersionCode(): Long {
        val context = BaseApplication.getAppContext()
        return getPkgVersionCode(context, context.packageName)
    }

    @JvmStatic
    fun getPkgVersionCode(context: Context, pkgName: String?): Long {
        try {
            val info =
                context.packageManager.getPackageInfo(pkgName!!, PackageManager.GET_META_DATA)
            return info.longVersionCode
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getPkgVersionCode, exception: " + e.message)
        }
        return -1L
    }

    @JvmStatic
    private fun fixString(str: String?): CharSequence? {
        return str?.replace("_", "") ?: ""
    }

    @JvmStatic
    fun getAppName(): String? {
        val appName = BaseApplication.getAppContext().packageName
        DebugUtil.i(TAG, "AppName->$appName")
        return appName
    }

    @JvmStatic
    fun getAppName(packageName: String): String {
        return try {
            BaseApplication.getAppContext().packageManager.let {
                it.getApplicationLabel(it.getApplicationInfo(packageName, 0)).toString()
            }
        } catch (e: Exception) {
            DebugUtil.w(TAG, "getAppName: exception : $e")
            ""
        }
    }

    @JvmStatic
    fun isAppInstalled(packageName: String): Boolean {
        if (TextUtils.isEmpty(packageName)) {
            return false
        }
        val packageInfo: PackageInfo? = try {
            BaseApplication.getAppContext().packageManager
                .getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "isNoteInstalledAndVersionEnough : e = ", e)
            return false
        }
        return if (packageInfo != null) {
            packageName == packageInfo.packageName
        } else {
            DebugUtil.e(TAG, "isNoteInstalledAndVersionEnough packageInfo is null return false ")
            false
        }
    }

    @JvmStatic
    fun metaDataEquals(packageName: String?, metaDataName: String?): Boolean {
        if (TextUtils.isEmpty(packageName)) {
            return false
        }
        var applicationInfo: ApplicationInfo? = null
        try {
            applicationInfo = BaseApplication.getAppContext().packageManager
                .getApplicationInfo(packageName!!, PackageManager.GET_META_DATA)
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "metaDataEquals : e", e)
        }
        if (applicationInfo == null) {
            DebugUtil.e(TAG, "metaDataEquals applicationInfo is null return false ")
            return false
        }
        val metaDataValue = applicationInfo.metaData.getBoolean(metaDataName)
        DebugUtil.d(TAG, "metaDataEquals metaDataValue is $metaDataValue")
        return metaDataValue
    }

    @JvmStatic
    fun isActionSupport(packageName: String?, action: String?): Boolean {
        if (TextUtils.isEmpty(packageName) || TextUtils.isEmpty(action)) {
            return false
        }
        var resolveInfo: List<ResolveInfo?>? = null
        try {
            val packageManager: PackageManager = BaseApplication.getAppContext().packageManager
            val intent = Intent(action)
            resolveInfo = packageManager.queryIntentActivities(
                intent,
                PackageManager.MATCH_DEFAULT_ONLY
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "isActionSupport : e", e)
        }
        return resolveInfo != null && resolveInfo.isNotEmpty()
    }

    /**
     * 需要manifest正确配置package包名
     */
    @Suppress("DEPRECATION")
    @JvmStatic
    fun metaDataInt(packageName: String, metaDataName: String): Int {
        if (packageName.isEmpty()) return -1
        try {
            val applicationInfo =
                BaseApplication.getAppContext().packageManager.getApplicationInfo(packageName, PackageManager.GET_META_DATA)
            return applicationInfo.metaData?.getInt(metaDataName, -1) ?: -1
        } catch (_: Exception) {
        }
        return -1
    }

    /**
     * 获取便签app的包名
     */
    @JvmStatic
    fun getNotesPackageName(): String {
        return if (BaseUtil.isOnePlusExp()) {
            "com.oneplus.note"
        } else {
            "com.coloros.note"
        }
    }

    /**
     * 获取日历app的包名
     */
    @JvmStatic
    fun getCalendarPackageName(): String {
        return if (BaseUtil.isEXP()) {
            "com.oplus.calendar"
        } else {
            "com.coloros.calendar"
        }
    }

    /**
     * 禅定模式下支持名单内是否不包含xx
     * @return true 禅定模式且不包含packageName
     * @return false 非禅定模式or包含packageName
     */
    @JvmStatic
    fun isBreathModeNotContainApp(context: Context, packageName: String): Boolean {
        val contentResolver = context.contentResolver
        val inMode = Settings.Secure.getInt(contentResolver, IS_BREATH_MODE, 0)
        DebugUtil.d(TAG, "isBreathModeNotContain,inMode=$inMode")
        if (inMode == 1) {
            val whiteListStr = Settings.Secure.getString(contentResolver, BREATH_MODE_WHITE_LIST) ?: ""
            DebugUtil.i(TAG, "isBreathModeNotContain,packageName=$packageName, whiteListStr=$whiteListStr")
            return !whiteListStr.contains(packageName)
        }
        return false
    }
}