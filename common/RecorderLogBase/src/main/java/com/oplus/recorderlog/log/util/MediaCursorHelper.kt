package com.oplus.recorderlog.log.util

import android.content.Context
import android.provider.MediaStore
import com.oplus.recorderlog.log.constants.XLogRecordConstants
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.DIR_CALL
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.DIR_INTERVIEW
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.DIR_MEETING
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.DIR_STANDARD
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.MIMETYPE_ACC
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.MIMETYPE_ACC_ADTS
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.MIMETYPE_AMR
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.MIMETYPE_AMR_WB
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.MIMETYPE_MP3
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.MIMETYPE_WAV
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORDINGS
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORD_TYPE_ALL
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORD_TYPE_CALL
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORD_TYPE_INTERVIEW
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORD_TYPE_MEETING
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORD_TYPE_STANDARD
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.STORAGE_RECORD_ABOVE_Q
import com.oplus.recorderlog.log.RecorderLogger
import com.oplus.recorderlog.util.BaseUtil
import com.oplus.recorderlog.util.BaseUtil.isAndroidQ
import com.oplus.recorderlog.util.BaseUtil.isAndroidQOrLater
import com.oplus.recorderlog.util.BaseUtil.isAndroidROrLater
import com.oplus.recorderlog.util.CommonFlavor
import java.io.File

object MediaCursorHelper {

    const val TAG = "MediaCursorHelper"

    private const val DEFAULT_DIR = "/storage/emulated/0"

    val sAcceptableAudioTypes = arrayOf<String>(
        MIMETYPE_AMR,
        MIMETYPE_WAV,
        MIMETYPE_MP3,
        MIMETYPE_AMR_WB,
        MIMETYPE_ACC_ADTS,
        MIMETYPE_ACC
    )
    private const val sAcceptableAudioTypesSQL = " in (?, ?, ?, ?, ?, ?) "

    fun getAllRecordForFilterAndQueryWhereClause(context: Context, filter: Int): String? {
        val supportFilterList: List<Int> = getAllSupportRecordForFilter(filter)
        val where: String = getRecordQueryWhereClause(context, supportFilterList)
        RecorderLogger.d(TAG, "getAllRecordForFilterAndQueryWhereClause where is $where", false)
        return where
    }

    fun getAllSupportRecordForFilter(filter: Int): List<Int> {
        val supportFilterList: MutableList<Int> = ArrayList()
        // 首页显示域不受支持模式影响，4个目录都显示
        supportFilterList.add(RECORD_TYPE_STANDARD)
        supportFilterList.add(RECORD_TYPE_INTERVIEW)
        supportFilterList.add(RECORD_TYPE_MEETING)
        supportFilterList.add(RECORD_TYPE_CALL)
        RecorderLogger.d(
            TAG, "filter: " + filter
                    + ", supportFilterList is " + supportFilterList, false
        )
        return if (filter == RECORD_TYPE_ALL) {
            supportFilterList
        } else if (supportFilterList.contains(filter)) {
            listOf(filter)
        } else {
            emptyList()
        }
    }

    private fun getRecordQueryWhereClause(context: Context, supportFilterList: List<Int>): String {
        var phoneDir: String? = BaseUtil.getPhoneStorageDir(context)
        if (phoneDir == null) {
            phoneDir = DEFAULT_DIR
        }
        val sdcardDir: String = BaseUtil.getSDCardStorageDir(context)
        var builder: StringBuilder = getRecordForFilterClause(supportFilterList, phoneDir, sdcardDir)
        if (CommonFlavor.getInstance().isOnePlus()) {
            builder = getOplusRecordQueryWhereClause(builder, phoneDir, sdcardDir)
        }
        builder = getMethordRecordQueryWhereClause(builder)
        RecorderLogger.d(TAG, "getRecordQueryWhereClause == $builder", false)
        return builder.toString()
    }

    private fun getMethordRecordQueryWhereClause(builder: java.lang.StringBuilder): java.lang.StringBuilder {
        val isAndroidQOrLater: Boolean = isAndroidQOrLater
        var whereBuilder = java.lang.StringBuilder()
        whereBuilder.append("(" + MediaStore.Audio.Media.MIME_TYPE + " " + sAcceptableAudioTypesSQL + ") ")
        whereBuilder = if (isAndroidQOrLater) {
            getWhereRelativePathMethod(whereBuilder, builder)
        } else {
            getWhereDATAMethod(whereBuilder, builder)
        }
        whereBuilder.append(" AND (" + MediaStore.Audio.Media.SIZE + "!=" + 0 + ")")
        return whereBuilder
    }

    private fun getWhereRelativePathMethod(
        whereBuilder: java.lang.StringBuilder,
        builder: java.lang.StringBuilder
    ): java.lang.StringBuilder {
        return whereBuilder.append(" AND ")
            .append(MediaStore.Audio.Media.RELATIVE_PATH)
            .append(" COLLATE NOCASE in (")
            .append(builder).append(")")
    }

    private fun getWhereDATAMethod(
        whereBuilder: java.lang.StringBuilder,
        builder: java.lang.StringBuilder
    ): java.lang.StringBuilder {
        return whereBuilder.append(" AND (( ")
            .append(builder).append("))")
    }

    fun getRecordForFilterClause(
        supportFilterList: List<Int>?,
        phoneDir: String,
        sdcardDir: String
    ): java.lang.StringBuilder {
        val standardRelativePath: String = BaseUtil.getRelativePathByRecordType(
            RECORD_TYPE_STANDARD,
                false
            )
        val meetingRelativePath: String = BaseUtil.getRelativePathByRecordType(
            RECORD_TYPE_MEETING,
                false
            )
        val interviewRelativePath: String = BaseUtil.getRelativePathByRecordType(
            RECORD_TYPE_INTERVIEW,
                false
            )
        val callRelativePath: String = BaseUtil.getRelativePathByRecordType(
            RECORD_TYPE_CALL,
                false
            )
        val normalPhoneDir = phoneDir + File.separator + standardRelativePath
        val normalSdDir = sdcardDir + File.separator + standardRelativePath
        val normalParentPhoneDir =
            phoneDir + File.separator + RECORDINGS
        val normalParentSdDir =
            sdcardDir + File.separator + RECORDINGS
        val interViewPhoneDir = phoneDir + File.separator + interviewRelativePath
        val interViewSdDir = sdcardDir + File.separator + interviewRelativePath
        val meetingPhoneDir = phoneDir + File.separator + meetingRelativePath
        val meetingSdDir = sdcardDir + File.separator + meetingRelativePath
        val callPhoneDir = phoneDir + File.separator + callRelativePath
        val callSdDir = sdcardDir + File.separator + callRelativePath
        val isAndroidQOrLater: Boolean = BaseUtil.isAndroidQOrLater
        val newSupportLists: List<Int> = checkSupportLists(supportFilterList)
        val builder = java.lang.StringBuilder()
        //默认读取Recordings目录文件
        if (isAndroidQOrLater) {
            builder.append(addQuotation(STORAGE_RECORD_ABOVE_Q))
        } else {
            builder.append(MediaStore.Audio.Media.DATA + " LIKE '")
                .append(normalParentPhoneDir).append("%' OR ")
                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                .append(normalParentSdDir).append("%'")
        }
        for (pos in newSupportLists.indices) {
            when (newSupportLists[pos]) {
                RECORD_TYPE_STANDARD -> {
                    if (isAndroidQOrLater) {
                        builder.append(",")
                            .append(mergeRelativePath(DIR_STANDARD))
                    } else {
                        builder.append(" OR ")
                            .append(MediaStore.Audio.Media.DATA + " LIKE '")
                            .append(normalPhoneDir).append("%' OR ")
                            .append(MediaStore.Audio.Media.DATA + " LIKE '")
                            .append(normalSdDir).append("%'")
                    }
                }
                RECORD_TYPE_INTERVIEW -> {
                    if (isAndroidQOrLater) {
                        builder.append(",")
                            .append(mergeRelativePath(DIR_INTERVIEW))
                    } else {
                        builder.append(" OR ")
                            .append(MediaStore.Audio.Media.DATA + " LIKE '")
                            .append(interViewPhoneDir).append("%' OR ")
                            .append(MediaStore.Audio.Media.DATA + " LIKE '")
                            .append(interViewSdDir).append("%'")
                    }
                }
                RECORD_TYPE_MEETING -> {
                    if (isAndroidQOrLater) {
                        builder.append(",")
                            .append(mergeRelativePath(DIR_MEETING))
                    } else {
                        builder.append(" OR ")
                            .append(MediaStore.Audio.Media.DATA + " LIKE '")
                            .append(meetingPhoneDir).append("%' OR ")
                            .append(MediaStore.Audio.Media.DATA + " LIKE '")
                            .append(meetingSdDir).append("%'")
                    }
                }
                RECORD_TYPE_CALL -> {
                    if (isAndroidQOrLater) {
                        builder.append(",")
                            .append(mergeRelativePath(DIR_CALL))
                    } else {
                        builder.append(" OR ")
                            .append(MediaStore.Audio.Media.DATA + " LIKE '")
                            .append(callPhoneDir).append("%' OR ")
                            .append(MediaStore.Audio.Media.DATA + " LIKE '")
                            .append(callSdDir).append("%'")
                    }
                }
            }
        }
        RecorderLogger.d(TAG, builder.toString(), false)
        return builder
    }

    private fun checkSupportLists(supportFilter: List<Int>?): List<Int> {
        //if the filter list is empty or contains all
        val newSupportFilter: MutableList<Int> = java.util.ArrayList()
        if (supportFilter == null || supportFilter.isEmpty()) {
            addAllSupportList(newSupportFilter)
        } else if (supportFilter.contains(XLogRecordConstants.RECORD_TYPE_ALL)) {
            addAllSupportList(newSupportFilter)
        } else {
            newSupportFilter.addAll(supportFilter)
        }
        return newSupportFilter
    }

    private fun addAllSupportList(supportFilter: MutableList<Int>) {
        supportFilter.add(RECORD_TYPE_STANDARD)
        supportFilter.add(RECORD_TYPE_INTERVIEW)
        supportFilter.add(RECORD_TYPE_MEETING)
        supportFilter.add(RECORD_TYPE_CALL)
    }

    private fun getOplusRecordQueryWhereClause(
        builder: java.lang.StringBuilder,
        phoneDir: String,
        sdcardDir: String
    ): java.lang.StringBuilder {
        var builder = builder
        val isAndroidQOrLater: Boolean = isAndroidQOrLater
        RecorderLogger.d(TAG, "builder$builder", false)
        if (isAndroidQOrLater) {
            var oplusRelative = ""
            if (isAndroidROrLater) {
                oplusRelative = XLogRecordConstants.OP_STORAGE_RECORD_ABOVE_AND_R
            } else if (isAndroidQ) {
                oplusRelative = XLogRecordConstants.OP_STORAGE_RECORD_BELOW_Q
            }
            builder = mergeOPRelativePath(builder, ",", oplusRelative)
            RecorderLogger.d(TAG, "builder$builder", false)
        } else {
            val oplusRelativePath: String = XLogRecordConstants.OP_STORAGE_RECORD_BELOW_Q
            val oneplusPhoneDir = phoneDir + File.separator + oplusRelativePath
            val onePlusSdDir = sdcardDir + File.separator + oplusRelativePath
            builder = builder.append(" OR ")
                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                .append(oneplusPhoneDir).append("%' OR ")
                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                .append(onePlusSdDir).append("%'")
        }
        return builder
    }

    private fun mergeOPRelativePath(
        builder: java.lang.StringBuilder,
        commaSymbol: String,
        oneplusRelative: String
    ): java.lang.StringBuilder {
        builder.append(commaSymbol)
        builder.append(addQuotation(oneplusRelative + File.separator))
        return builder
    }

    private fun mergeRelativePath(dir: String): String? {
        return addQuotation(STORAGE_RECORD_ABOVE_Q + dir + File.separator)
    }

    private fun addQuotation(src: String): String? {
        return "'$src'"
    }
}