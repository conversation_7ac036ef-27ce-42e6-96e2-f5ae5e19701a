package com.oplus.recorderlog.log.constants

import android.net.Uri
import com.oplus.recorderlog.util.CommonFlavor

class XLogDbConstant {

    companion object {

        const val PACKAGE_NAME_FOR_ONEPLUS_EXPORT = "com.oneplus.soundrecorder"
        const val PACKAGE_NAME_FOR_OTHER = "com.oneplus.soundrecorder"

        const val PATH_RECORD = "records"
        const val PATH_STATUS = "status"

        val PACKAGE_NAME: String = CommonFlavor.getInstance().getPackageName()
        val AUTHORITY = "$PACKAGE_NAME.provider"


        private val CONTENT_AUTHORITY = "content://" + AUTHORITY


        fun getContentUri(volumeName: String): Uri {
            return Uri.parse("$CONTENT_AUTHORITY/$volumeName")
        }


        private fun getAppnedContentUri(volumeName: String, id: Long): Uri {
            return Uri.parse("$CONTENT_AUTHORITY/$volumeName/$id")
        }
    }

    object RecorderColumn {
        const val COLUMN_NAME_ID = "_id"
        const val COLUMN_NAME_UUID = "uuid"
        const val COLUMN_NAME_DATA = "_data"
        const val COLUMN_NAME_SIZE = "size"
        const val COLUMN_NAME_DISPLAY_NAME = "display_name"
        const val COLUMN_NAME_MIMETYPE = "mime_type"
        const val COLUMN_NAME_DATE_CREATED = "date_added"
        const val COLUMN_NAME_DATE_MODIFIED = "date_modified"
        const val COLUMN_NAME_RECORD_TYPE = "record_type"
        const val COLUMN_NAME_MARK_DATA = "mark_data"
        const val COLUMN_NAME_AMP_DATA = "amplitude_data"
        const val COLUMN_NAME_DURATION = "duration"
        const val COLUMN_NAME_BUCKET_ID = "bucket_id"
        const val COLUMN_NAME_BUCKET_DISPLAY_NAME = "bucket_display_name"
        const val COLUMN_NAME_DIRTY = "dirty"
        const val COLUMN_NAME_DELETE = "deleted"
        const val COLUMN_NAME_MD5 = "md5"
        const val COLUMN_NAME_FILE_ID = "file_id"
        const val COLUMN_NAME_GLOBAL_ID = "global_id"
        const val COLUMN_NAME_SYNC_TYPE = "sync_type"
        const val COLUMN_NAME_SYNC_UPLOAD_STATUS = "sync_upload_status"
        const val COLOUM_NAME_SYNC_DOWNLOAD_STATUS = "sync_download_status"
        const val COLUMN_NAME_ERROR_CODE = "error_code"
        const val COLUMN_NAME_LEVEL = "level"
        const val COLUMN_NAME_LOCAL_EDIT_STATUS = "editStatus"
        const val COLUMN_NAME_SYNC_DATE = "sync_date"
        const val COLUMN_NAME_FAIL_COUNT = "failed_count"
        const val COLUMN_NAME_LAST_FAIL_TIME = "last_failed_time"
        const val COLUMN_NAME_RELATIVE_PATH = "relative_path"
        const val COLUMN_NAME_AMP_FILE_PATH = "amp_file_path"
        const val COLUMN_NAME_PRIVATE_STATUS = "private_status"
        const val COLUMN_NAME_MIGRATE_STATUS = "migrate_status"
        const val COLUMN_NAME_IS_MARKLIST_SHOWING = "is_marklist_showing"
        const val COLUMN_NAME_RECORD_KEY_ID_FOR_OHTER_TABLE = "key_id"

        // 对应cloudKit 恢复数据元数据版本号sysVersion
        const val COLUMN_NAME_CLOUD_SYS_VERSION = "sys_version"

        // 对应cloudKit上传文件返回checkPayload，临时使用，备份上传Create元数据传入，校验文件在云端是否
        const val COLUMN_NAME_CLOUD_CHECK_PAYLOAD = "file_checkPayload"
    }


    object RecordUri {
        val RECORD_CONTENT_URI: Uri = getContentUri(PATH_RECORD)

        fun getAppnedRecordContentUri(id: Long): Uri {
            return getAppnedContentUri(PATH_RECORD, id)
        }

        fun getAppnedStatusContentUri(id: Long): Uri {
            return getAppnedContentUri(PATH_STATUS, id)
        }
    }
}