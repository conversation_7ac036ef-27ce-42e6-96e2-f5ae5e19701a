/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NotificationPermissionSnackBarTransparentActivityTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.permission

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.coui.appcompat.snackbar.COUISnackBar
import com.soundrecorder.common.shadows.ShadowCOUISnackBar
import com.soundrecorder.common.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.shadows.ShadowOplusUsbEnvironment
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class,
    ShadowFeatureOption::class, ShadowCOUIVersionUtil::class, ShadowCOUISnackBar::class])
class NotificationPermissionSnackBarTransparentActivityTest {
    private var mContext: Context? = null
    private var mActivityController: ActivityController<NotificationPermissionSnackBarTransparentActivity>? = null
    private var mActivity: NotificationPermissionSnackBarTransparentActivity? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mActivityController = Robolectric.buildActivity(
            NotificationPermissionSnackBarTransparentActivity::class.java)
    }

    @After
    fun tearDown() {
        mActivity = null
        mActivityController = null
        mContext = null
    }

    @Test
    fun should_finish_when_activity_from_restore() {
        val activity = mActivityController?.create()?.get()
        Assert.assertFalse(activity?.isFinishing ?: true)
    }

    @Test
    fun should_finish_when_activity_from_restore2() {
        val activity = mActivityController?.create()?.resume()?.recreate()?.get()
        Assert.assertTrue(activity?.isFinishing ?: false)
    }

    @Test
    fun verify_value_when_activity_started() {
        val activity = mActivityController?.create()?.resume()?.get()
        Assert.assertNotEquals(0, activity?.window?.navigationBarColor ?: 0)

        Assert.assertNotNull(Whitebox.getInternalState(activity, "mOutSideView"))

        val snackBar = Whitebox.getInternalState<COUISnackBar>(activity, "mSnackBar")
        Assert.assertNotNull(snackBar)
        snackBar.show()
        Whitebox.invokeMethod<Void>(activity, "checkShouldDismissSnackBarAndFinish")
    }

    @Test
    fun verify_value_when_activity_showSnackBar() {
        val mockStatic = Mockito.mockStatic(PermissionUtils::class.java)
        mockStatic.`when`<Boolean> { PermissionUtils.hasNotificationPermission() }.thenReturn(false)
        Assert.assertFalse(PermissionUtils.hasNotificationPermission())

        val activity = mActivityController?.create()?.resume()?.get()
        Assert.assertFalse(activity?.isFinishing ?: true)
        val snackBar = Whitebox.getInternalState<COUISnackBar>(activity, "mSnackBar")
        Assert.assertNotNull(Whitebox.getInternalState<COUISnackBar>(activity, "mOutSideView"))
        Assert.assertNotNull(snackBar)
        Whitebox.invokeMethod<Void>(activity, "showSnackBar")
//        Assert.assertTrue(snackBar.isShown)

        mockStatic.`when`<Boolean> { PermissionUtils.hasNotificationPermission() }.thenReturn(true)
        Assert.assertTrue(PermissionUtils.hasNotificationPermission())
        Whitebox.invokeMethod<Void>(activity, "showSnackBar")
        Assert.assertFalse(snackBar.isShown)
        Assert.assertTrue(activity?.isFinishing ?: false)

        mockStatic.reset()
        mockStatic.close()
    }


    @Test
    fun should_activity_finish_when_backPressed() {
        val activity = mActivityController?.create()?.resume()?.get()
        activity?.onBackPressed()
        Assert.assertTrue(activity?.isFinishing ?: false)
    }

    @Test
    fun should_activity_finish_when_onResume() {
        var activity = mActivityController?.create()?.resume()?.get()
        Assert.assertFalse(activity?.isFinishing ?: true)

        val mockStatic = Mockito.mockStatic(PermissionUtils::class.java)
        mockStatic.`when`<Boolean> { PermissionUtils.hasNotificationPermission() }.thenReturn(true)
        activity = mActivityController?.resume()?.get()
        Assert.assertFalse(activity?.isFinishing ?: true)

        mockStatic.reset()
        mockStatic.close()
    }
}