/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AnimatedCircleButtonTest
 * Description:
 * Version: 1.0
 * Date: 2023/12/11
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/11 1.0 create
 */

package com.soundrecorder.common.widget

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.os.Build
import android.view.MotionEvent
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class AnimatedCircleButtonTest {
    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        context = null
    }

    @Test
    fun should_correct_when_refreshCircleRadius() {
        val context = context ?: return
        val button = AnimatedCircleButton(context, null)
        button.refreshCircleRadius(1F)
        Assert.assertEquals(1, Whitebox.getInternalState(button, "mCircleRadius"))
    }

    @Test
    fun should_correct_when_isMinPressedTime() {
        val context = context ?: return
        val systemMill = System.currentTimeMillis()
        val button = AnimatedCircleButton(context, null)
        Whitebox.setInternalState(button, "mDownTime", systemMill - 100)
        var isMinPressedTime = Whitebox.invokeMethod<Boolean>(button, "isMinPressedTime")
        Assert.assertFalse(isMinPressedTime)

        Whitebox.setInternalState(button, "mDownTime", systemMill - 5)
        isMinPressedTime = Whitebox.invokeMethod<Boolean>(button, "isMinPressedTime")
        Assert.assertTrue(isMinPressedTime)
    }

    @Test
    fun should_notNull_when_createDownAnimator() {
        val context = context ?: return
        val button = AnimatedCircleButton(context, null)
        val animator = Whitebox.invokeMethod<ValueAnimator>(button, "createDownAnimator")
        Assert.assertNotNull(animator)
    }

    @Test
    fun should_notNull_when_createUpAnimator() {
        val context = context ?: return
        val button = AnimatedCircleButton(context, null)
        val animator = Whitebox.invokeMethod<ValueAnimator>(button, "createUpAnimator", 0f)
        Assert.assertNotNull(animator)
    }

    @Test
    fun should_notNull_when_createEnterAnimator() {
        val context = context ?: return
        val button = AnimatedCircleButton(context, null)
        Assert.assertNotNull(button.createEnterAnimator(0f))
    }

    @Test
    fun should_correct_when_switchPauseState() {
        val context = context ?: return
        val button = AnimatedCircleButton(context, null)
        button.switchPauseState()
        Assert.assertEquals(Whitebox.getInternalState(button, "mDrawablePause"), button.drawable)

        button.switchPlayState()
        Assert.assertEquals(Whitebox.getInternalState(button, "mDrawablePlay"), button.drawable)
    }

    @Test
    fun should_correct_when_setCircleColor() {
        val context = context ?: return
        val button = AnimatedCircleButton(context, null)
        button.setCircleColor(true)
        Assert.assertNotEquals(AnimatedCircleButton.DEFAULT_CIRCLE_COLOR, button.mCircleColor)

        button.setCircleColor(false)
        Assert.assertNotEquals(AnimatedCircleButton.DEFAULT_CIRCLE_COLOR, button.mCircleColor)
    }

    @Test
    fun should_returnTrue_when_onTouchEvent() {
        val context = context ?: return
        val button = AnimatedCircleButton(context, null)
        button.animEnter = false
        Assert.assertFalse(button.onTouchEvent(Mockito.mock(MotionEvent::class.java)))
    }

    @Test
    fun should_correct_when_onDetachedFromWindow() {
        val context = context ?: return
        val nameDrawablePlay = "mDrawablePlay"
        val button = AnimatedCircleButton(context, null)
        Assert.assertNotNull(Whitebox.getInternalState(button, "mCirclePaint"))
        Assert.assertNull(Whitebox.getInternalState(button, nameDrawablePlay))

        button.switchPlayState()
        Assert.assertNotNull(Whitebox.getInternalState(button, nameDrawablePlay))

        Whitebox.invokeMethod<Unit>(button, "onDetachedFromWindow")
        Assert.assertNull(Whitebox.getInternalState(button, "mCirclePaint"))
        Assert.assertNull(Whitebox.getInternalState(button, nameDrawablePlay))
    }

    @Test
    fun should_correct_when_drawCircle() {
        val context = context ?: return
        val mockCanvas = Mockito.spy(Canvas::class.java)
        val button = AnimatedCircleButton(context, null)
        button.drawCircle(mockCanvas)
    }

    @Test
    fun should_correct_when_drawState() {
        val context = context ?: return
        val canvas = Mockito.mock(Canvas::class.java)
        val button = AnimatedCircleButton(context, null)
        button.drawState(canvas)
    }

    @Test
    fun should_correct_when_dispatchAnim() {
        val context = context ?: return
        val motionEvent = Mockito.mock(MotionEvent::class.java)
        Mockito.`when`(motionEvent.action)
            .thenReturn(MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE, MotionEvent.ACTION_UP)
        val button = AnimatedCircleButton(context, null)
        Whitebox.invokeMethod<Unit>(button, "dispatchAnim", motionEvent)
        Assert.assertNotEquals(0, Whitebox.getInternalState(button, "mDownTime"))

        Whitebox.invokeMethod<Unit>(button, "dispatchAnim", motionEvent)
        Whitebox.invokeMethod<Unit>(button, "dispatchAnim", motionEvent)
    }

    @Test
    fun should_correct_when_actionUp() {
        val context = context ?: return
        val funcName = "actionUp"
        val stateActionUp = "mActionUp"
        val button = AnimatedCircleButton(context, null)
        Whitebox.setInternalState(button, "mUpAnimatorStarted", true)
        Whitebox.invokeMethod<Unit>(button, funcName)
        Assert.assertFalse(Whitebox.getInternalState(button, stateActionUp))

        Whitebox.setInternalState(button, "mUpAnimatorStarted", false)
        val systemMill = System.currentTimeMillis()
        Whitebox.setInternalState(button, "mDownTime", systemMill - 5)
        Whitebox.invokeMethod<Unit>(button, funcName)
        Assert.assertTrue(Whitebox.getInternalState(button, "mIsDownMinTimeInterruptedAnimator"))
        Assert.assertFalse(Whitebox.getInternalState(button, stateActionUp))

        Whitebox.setInternalState(button, "mDownAnimatorEnd", true)
        Whitebox.invokeMethod<Unit>(button, funcName)
        Assert.assertTrue(Whitebox.getInternalState(button, stateActionUp))
    }

    @Test
    fun should_correct_when_dispatchVibrate() {
        val context = context ?: return
        val motionEvent = Mockito.mock(MotionEvent::class.java)
        Mockito.`when`(motionEvent.action)
            .thenReturn(MotionEvent.ACTION_DOWN, MotionEvent.ACTION_UP)

        val button = AnimatedCircleButton(context)
        Whitebox.setInternalState(button, "mVibrateToggle", true)
        Whitebox.invokeMethod<Unit>(button, "dispatchVibrate", motionEvent)

        Whitebox.invokeMethod<Unit>(button, "dispatchVibrate", motionEvent)
    }
}