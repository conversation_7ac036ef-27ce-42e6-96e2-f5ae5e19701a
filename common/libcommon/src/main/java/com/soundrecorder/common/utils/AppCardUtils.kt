/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AppCardUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.app.ActivityOptions
import android.content.Intent
import android.os.Bundle
import com.soundrecorder.base.utils.OSDKCompatUtils

object AppCardUtils {
    private const val OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED = 0x10000000
    private const val OPLUS_FLAG_ACTIVITY_CONTINUE_PRIVACY = 0x20000000
    private const val EXTRA_DESCRIPTION = "oplus.intent.extra.DESCRIPTION"

    @JvmStatic
    fun Intent.addContinueFlag(): Intent {
        if (DisplayUtils.supportOtherDisplay) {
            OSDKCompatUtils.setCompatFlags(this, OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED)
        }
        return this
    }

    @JvmStatic
    fun Intent.addContinueRequestPermissionFlag(description: CharSequence? = null): Intent {
        if (DisplayUtils.supportOtherDisplay) {
            OSDKCompatUtils.setCompatFlags(
                this,
                OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED or OPLUS_FLAG_ACTIVITY_CONTINUE_PRIVACY
            )
            if (description.isNullOrBlank().not()) {
                putExtra(EXTRA_DESCRIPTION, description)
            }
        }
        return this
    }

    @JvmStatic
    fun Int.launchDisplay(): Bundle {
        return ActivityOptions.makeBasic().apply { launchDisplayId = this@launchDisplay }.toBundle()
    }
}