/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : CallRecordService.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2020.06.23
 * History       :(ID,  2020.06.23, tianjun, Description)
 ************************************************************/
package com.soundrecorder.common.sync

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.app.JobIntentService
import com.soundrecorder.base.utils.DebugUtil

private const val TAG = "CallRecordingReceiver"

class CallRecordingReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context?, intent: Intent?) {

        context?.apply {
            val service = Intent()
            service.action = intent?.action
            service.data = intent?.data
            try {
                service.putParcelableArrayListExtra(Intent.EXTRA_STREAM, intent?.getParcelableArrayListExtra<Uri>(Intent.EXTRA_STREAM))
            } catch (e: Exception) {
                DebugUtil.e(TAG, "getParcelableArrayListExtra error.", e)
            }
            JobIntentService.enqueueWork(this, SyncCallRecordService::class.java,
                SyncCallRecordService.JOB_ID, service)
            DebugUtil.i(TAG, intent?.toString())
        }
    }
}