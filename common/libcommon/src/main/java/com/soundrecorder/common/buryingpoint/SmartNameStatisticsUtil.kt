/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - SmartNameStatisticsUtil.kt
 ** Description: SmartNameStatisticsUtil.
 ** Version: 1.0
 ** Date : 2025/4/7
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/4/7    1.0    create
 ****************************************************************/
package com.soundrecorder.common.buryingpoint

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.buryingpoint.RecorderUserAction.addNewCommonUserAction

object SmartNameStatisticsUtil {
    private const val EVENT_GROUP_SMART_NAME  = "smart_name"

    /*打开或者关闭智能命名开关*/
    private const val EVENT_ID_SWITCH_SMART_NAME = "event_switch_smart_name"
    private const val KEY_SMART_NAME_SWITCH = "switch"
    const val VALUE_SMART_NAME_OPEN = 1
    const val VALUE_SMART_NAME_CLOSE = 0

    /*智能命名结果*/
    private const val EVENT_ID_SMART_NAME_STATUS = "event_smart_name_status"
    private const val KEY_SMART_NAME_RESULT = "result"
    const val VALUE_SMART_NAME_RESULT_SUCCESS = 1
    const val VALUE_SMART_NAME_RESULT_FAILURE = 0
    private const val KEY_SMART_NAME_ERROR_CODE = "error"
    const val VALUE_SMART_NAME_ERROR_CODE_DEFAULT = 0
    const val VALUE_SMART_NAME_ERROR_CODE_NETWORK_ERROR = 1

    @JvmStatic
    fun addSmartNameSwitchStateEvent(switchState: Int) {
        val eventInfo: MutableMap<String, Int> = HashMap()
        eventInfo[KEY_SMART_NAME_SWITCH] = switchState
        addNewCommonUserAction(BaseApplication.getAppContext(), EVENT_GROUP_SMART_NAME, EVENT_ID_SWITCH_SMART_NAME, eventInfo, false)
    }

    @JvmStatic
    fun addSmartNameResultEvent(smartNameResult: Int, errorCode: Int) {
        val eventInfo: MutableMap<String, Int> = HashMap()
        eventInfo[KEY_SMART_NAME_RESULT] = smartNameResult
        eventInfo[KEY_SMART_NAME_ERROR_CODE] = errorCode
        addNewCommonUserAction(BaseApplication.getAppContext(), EVENT_GROUP_SMART_NAME, EVENT_ID_SMART_NAME_STATUS, eventInfo, false)
    }
}