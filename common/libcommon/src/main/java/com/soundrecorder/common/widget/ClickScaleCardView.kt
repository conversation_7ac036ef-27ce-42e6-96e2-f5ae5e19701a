/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/3/3
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R

open class ClickScaleCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr, defStyleRes) {

    companion object {
        const val TAG = "ClickScaleCardView"
    }

    private var feedbackHelper: COUIPressFeedbackHelper? = null
    private var excludeTagLists: List<String>? = null
    private var canUpAnimate = false

    private fun getFeedbackHelperNotNull(): COUIPressFeedbackHelper {
        if (feedbackHelper == null) {
            feedbackHelper =
                COUIPressFeedbackHelper(this, COUIPressFeedbackHelper.CARD_PRESS_FEEDBACK)
        }

        return feedbackHelper!!
    }

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.ClickScaleStyle)
        val idsStr = ta.getString(R.styleable.ClickScaleStyle_exclude_view_tags)
        ta.recycle()

        if (!idsStr.isNullOrEmpty()) {
            excludeTagLists = idsStr.split(",")
        }
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        DebugUtil.i(TAG, "dispatchTouchEvent: event is ${event?.action}")
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                if (!touchOnClickableView(event)) {
                    canUpAnimate = true
                    getFeedbackHelperNotNull().executeFeedbackAnimator(true)
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (canUpAnimate) {
                    getFeedbackHelperNotNull().executeFeedbackAnimator(false)
                }
            }
        }
        return super.dispatchTouchEvent(event)
    }

    /**
     * 用于判断点击事件 是否点击在了指定的view上面，用于事件屏蔽
     * 与交互 黄*军 沟通后不需加屏蔽事件，代码先保留
     */
    private fun touchOnClickableView(ev: MotionEvent?): Boolean {
        ev ?: return false
        excludeTagLists?.forEach {
            val childView = findViewWithTag<View>(it)
            DebugUtil.i(TAG, "touchOnClickableView, tag is $it")
            if (childView?.isVisible == true && isTouchPointView(childView, ev.rawX, ev.rawY)) {
                return true
            }
        }
        return false
    }

    /**
     * 判定点击区域
     */
    private fun isTouchPointView(view: View, x: Float, y: Float): Boolean {
        val location = intArrayOf(0, 0)
        view.getLocationOnScreen(location)

        val left = (location[0] - view.paddingLeft).toFloat()
        val top = (location[1] - view.paddingTop).toFloat()
        val right = left + view.measuredWidth + view.paddingRight
        val bottom = top + view.measuredHeight + view.paddingBottom
        DebugUtil.i(TAG, "event is ($x, $y), location is (${location[0]}, ${location[1]}), view is ($left, $top,$right,$bottom)")
        if (y in top..bottom && x in left..right) {
            return true
        }
        return false
    }

    open fun onRelease() {
    }
}