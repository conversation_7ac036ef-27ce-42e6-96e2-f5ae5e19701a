/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ShadowViewDraw
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/6/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import com.soundrecorder.common.R
import androidx.core.content.withStyledAttributes
import com.soundrecorder.common.widget.AnimatedCircleButton

@SuppressLint("ResourceAsColor")
class ShadowViewDraw@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AnimatedCircleButton(context, attrs, defStyleAttr) {
    private var shadowBlurRadius = 0f
    private var shadowOffsetX = 0f
    private var spread = 0f
    private var shadowOffsetY = 0f
    private var shadowColor = context.resources.getColor(R.color.record_save_btn_shadow)

    init {
        setWillNotDraw(false)
        getContext().withStyledAttributes(
            attrs,
            R.styleable.ShadowViewDraw
        ) {
            shadowOffsetX = getFloat(R.styleable.ShadowViewDraw_shadowOffsetX, 0f)
            shadowOffsetY = getFloat(R.styleable.ShadowViewDraw_shadowOffsetY, 0f)
            shadowBlurRadius = getFloat(R.styleable.ShadowViewDraw_shadowBlurRadius, 0f)
            shadowColor =
                getColor(R.styleable.ShadowViewDraw_shadowColor, R.color.record_save_btn_shadow)
        }
    }

    private var paint: Paint = Paint().apply {
        color = Color.TRANSPARENT
        isAntiAlias = true
        setShadowLayer(shadowBlurRadius, shadowOffsetX, shadowOffsetY, shadowColor) // 参数：模糊半径、x偏移、y偏移、颜色
    }

    override fun onDraw(canvas: Canvas) {
        val centerX = width / 2
        val centerY = height / 2
        val radius = context.resources.getDimension(R.dimen.circle_record_button_diam) / 2
        paint.let { canvas.drawCircle(centerX.toFloat(), centerY.toFloat(), radius, it) }
        super.onDraw(canvas)
    }
}