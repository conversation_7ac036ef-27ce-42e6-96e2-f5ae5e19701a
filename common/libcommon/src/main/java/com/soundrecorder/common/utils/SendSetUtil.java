/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.utils;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import com.soundrecorder.base.utils.OplusCompactUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.constant.OplusCompactConstant;

public class SendSetUtil {
    public static final int REQUEST_CODE_SET_RING = 30002;
    private static final String TAG = "SendSetUtil";

    public static void setAs(long id, Activity c) {
        Intent intent = new Intent();
        intent = OplusCompactUtil.getActionForIntent(
                intent,
                OplusCompactConstant.SETTING_RINGTONE_ACTION_BEFOR,
                OplusCompactConstant.SETTING_RINGTONE_ACTION_AFTER
        );
        intent.putExtra("audio_id", id);
        /*
         * Action:com.oppo.music.set_ringtone
         * extra : audio_id or audio_uri
         */
        intent.setPackage("com.android.settings");
        try {
            c.startActivityForResult(intent, REQUEST_CODE_SET_RING);
            c.overridePendingTransition(
                    com.support.appcompat.R.anim.coui_push_up_enter_activitydialog,
                    com.support.appcompat.R.anim.coui_push_down_exit_activitydialog
            );
        } catch (ActivityNotFoundException ex) {
            DebugUtil.e(TAG, "set as ActivityNotFoundException", ex);
        } catch (Exception e) {
            DebugUtil.e(TAG, "set as Exception", e);
        }
    }
}
