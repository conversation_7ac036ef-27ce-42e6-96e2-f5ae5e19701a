# 裁剪页面约束关系动态修改实现说明

本文档详细说明了在EditRecordFragment中实现的约束关系动态修改功能。

## 功能概述

在裁剪页面中，根据标记列表的显示/隐藏状态，动态修改相关视图的约束关系：

- **标记列表不显示时**: `anim_title`底部和`preViewBar_and_cutLayout`顶部约束，间距40dp
- **标记列表显示时**: 按照XML布局内现有的约束关系设置，包括分割线和所有相关视图

## 实现位置

**主要文件**: `page/editRecord/src/main/java/com/soundrecorder/editrecord/ui/EditRecordFragment.kt`

## 核心方法

### 1. updateConstraintsForMarkListVisibility()

**功能**: 根据标记列表可见性动态修改约束关系

**参数**:
- `isMarkListVisible: Boolean` - 标记列表是否显示
- `dataBinding: EditRecordLayoutBinding` - 数据绑定对象

**实现逻辑**:

#### 标记列表显示时 (isMarkListVisible = true)
```kotlin
// 使用XML布局内现有的约束关系
// anim_title -> layout_marklist -> edit_mark_list_footer_divider -> preViewBar_and_cutLayout

constraintSet.connect(R.id.layout_marklist, ConstraintSet.TOP, R.id.anim_title, ConstraintSet.BOTTOM)
constraintSet.connect(R.id.layout_marklist, ConstraintSet.BOTTOM, R.id.edit_mark_list_footer_divider, ConstraintSet.TOP)
constraintSet.connect(R.id.edit_mark_list_footer_divider, ConstraintSet.TOP, R.id.layout_marklist, ConstraintSet.BOTTOM)
constraintSet.connect(R.id.edit_mark_list_footer_divider, ConstraintSet.BOTTOM, R.id.preViewBar_and_cutLayout, ConstraintSet.TOP)
constraintSet.connect(R.id.preViewBar_and_cutLayout, ConstraintSet.TOP, R.id.edit_mark_list_footer_divider, ConstraintSet.BOTTOM)
```

#### 标记列表隐藏时 (isMarkListVisible = false)
```kotlin
// anim_title底部和preViewBar_and_cutLayout顶部约束，间距40dp
// anim_title -> (40dp间距) -> preViewBar_and_cutLayout

// 获取40dp的像素值
val margin40dp = dataBinding.root.context.resources.getDimensionPixelSize(
    com.soundrecorder.common.R.dimen.dp40
)

constraintSet.clear(R.id.anim_title, ConstraintSet.BOTTOM)
constraintSet.clear(R.id.preViewBar_and_cutLayout, ConstraintSet.TOP)
constraintSet.connect(R.id.anim_title, ConstraintSet.BOTTOM, R.id.preViewBar_and_cutLayout, ConstraintSet.TOP, margin40dp)
constraintSet.connect(R.id.preViewBar_and_cutLayout, ConstraintSet.TOP, R.id.anim_title, ConstraintSet.BOTTOM, margin40dp)
```

### 2. 调用时机

在`checkNeedHideOtherView()`方法中的两个关键位置调用：

#### 位置1: 标记列表显示时
```kotlin
if ((contentHeight - waveHeight - titleHeight - markItemHeight - dragBarHeight - centerDividerHeight) >= 0) {
    waveView.isVisible = true
    markListView.isVisible = hasMarkData
    animTitleView.isVisible = true
    dragBarView.isVisible = true

    // 动态修改约束关系
    updateConstraintsForMarkListVisibility(hasMarkData, dataBinding)

    // ... 其他逻辑
}
```

#### 位置2: 标记列表隐藏时
```kotlin
markListView.isVisible = false // 隐藏标记

// 动态修改约束关系 - 标记列表隐藏时
updateConstraintsForMarkListVisibility(false, dataBinding)

// ... 其他逻辑
```

## 涉及的视图组件

### 主要视图
1. **anim_title** (`LinearLayout`) - 时间和录音名称显示区域
2. **layout_marklist** (`LinearLayout`) - 标记列表容器
3. **edit_mark_list_footer_divider** (`View`) - 标记列表底部分割线
4. **preViewBar_and_cutLayout** (`ConstraintLayout`) - 预览条和裁剪控制区域

### 约束关系图

#### 标记列表显示时
```
anim_title
    ↓ (bottom -> top)
layout_marklist
    ↓ (bottom -> top)
edit_mark_list_footer_divider
    ↓ (bottom -> top)
preViewBar_and_cutLayout
```

#### 标记列表隐藏时
```
anim_title
    ↓ (bottom -> top)
preViewBar_and_cutLayout
```

## 分割线处理

分割线的可见性与标记列表的显示状态同步：

```kotlin
dataBinding.editMarkListFooterDivider.visibility =
    if (isMarkListVisible) View.VISIBLE else View.INVISIBLE
```

## 技术要点

### 1. ConstraintSet使用
- 使用`ConstraintSet.clone()`复制当前约束
- 使用`ConstraintSet.connect()`建立新约束
- 使用`ConstraintSet.clear()`清除现有约束
- 使用`ConstraintSet.applyTo()`应用约束变化

### 2. 约束清理
在建立新约束前，必须先清理冲突的旧约束：
```kotlin
constraintSet.clear(R.id.anim_title, ConstraintSet.BOTTOM)
constraintSet.clear(R.id.preViewBar_and_cutLayout, ConstraintSet.TOP)
```

### 3. 双向约束
确保约束关系是双向的，避免布局异常：
```kotlin
// A -> B
constraintSet.connect(R.id.viewA, ConstraintSet.BOTTOM, R.id.viewB, ConstraintSet.TOP)
// B -> A
constraintSet.connect(R.id.viewB, ConstraintSet.TOP, R.id.viewA, ConstraintSet.BOTTOM)
```

## 调试信息

添加了详细的调试日志：
```kotlin
DebugUtil.d(TAG, "updateConstraintsForMarkListVisibility: 标记列表显示，使用原有约束关系")
DebugUtil.d(TAG, "updateConstraintsForMarkListVisibility: 标记列表隐藏，直接约束anim_title和preViewBar_and_cutLayout")
```

## 测试验证

### 单元测试
**文件**: `page/editRecord/src/test/java/com/soundrecorder/editrecord/ui/EditRecordConstraintTest.kt`

**测试覆盖**:
1. 标记列表显示时的约束关系
2. 标记列表隐藏时的约束关系
3. 约束修改方法的调用
4. 视图可见性变化
5. 布局参数访问
6. 约束关系完整性
7. 性能测试

### 手动测试步骤
1. **进入裁剪页面**: 选择一个有标记的录音文件进入裁剪
2. **观察标记列表显示**: 确认标记列表、分割线正常显示
3. **触发标记列表隐藏**: 调整窗口大小或旋转屏幕
4. **验证约束变化**: 确认`anim_title`和`preViewBar_and_cutLayout`直接相邻
5. **恢复标记列表显示**: 确认恢复到原有布局

## 注意事项

### 1. 性能考虑
- 约束修改操作相对耗时，避免频繁调用
- 在布局变化时才进行约束修改

### 2. 兼容性
- 支持不同屏幕尺寸和方向
- 兼容分屏模式和折叠屏

### 3. 异常处理
- 确保dataBinding不为null
- 处理约束操作可能的异常

### 4. 维护性
- 约束关系变化需要同步更新此功能
- 新增相关视图时需要考虑约束影响

## 后续优化建议

1. **动画效果**: 为约束变化添加平滑的过渡动画
2. **缓存优化**: 缓存ConstraintSet对象，减少重复创建
3. **配置化**: 将约束关系配置化，便于维护
4. **监听机制**: 添加约束变化的监听回调
5. **自动化测试**: 增加UI自动化测试覆盖

## 相关文件

- **布局文件**: `page/editRecord/src/main/res/layout/edit_record_layout.xml`
- **大屏布局**: `page/editRecord/src/main/res/layout-w600dp/edit_record_layout.xml`
- **主要实现**: `page/editRecord/src/main/java/com/soundrecorder/editrecord/ui/EditRecordFragment.kt`
- **测试文件**: `page/editRecord/src/test/java/com/soundrecorder/editrecord/ui/EditRecordConstraintTest.kt`
