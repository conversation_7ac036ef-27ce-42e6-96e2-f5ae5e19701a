<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:lines="1"
        android:textColor="@color/color_notification_play_title"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        tools:text="测试标题" />

    <TextView
        android:id="@+id/timestamp_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_tv"
        android:layout_marginTop="@dimen/dp2"
        android:ellipsize="end"
        android:lines="1"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        android:textColor="@color/color_notification_timestamp"
        android:textSize="@dimen/sp14"
        tools:text="00:12/12:33" />
</merge>