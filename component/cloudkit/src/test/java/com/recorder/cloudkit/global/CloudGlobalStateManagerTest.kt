/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CloudGlobalStateManagerTest
 * Description:
 * Version: 1.0
 * Date: 2024/12/2
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/12/2 1.0 create
 */

package com.recorder.cloudkit.global

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.heytap.cloudkit.libsync.service.FunctionScopeRsp
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CloudGlobalStateManagerTest {
    private val propertyState = "sRecordGlobalState"
    private val propertySupport = "sIsCloudGlobalSupport"


    @Test
    fun should_correct_when_isInitSuccess() {
        CloudGlobalStateManager.reset()
        Assert.assertFalse(CloudGlobalStateManager.isInitSuccess())

        Whitebox.setInternalState(CloudGlobalStateManager::class.java, propertyState, CloudGlobalStateManager.GLOBAL_ERROR)
        Assert.assertFalse(CloudGlobalStateManager.isInitSuccess())

        Whitebox.setInternalState(CloudGlobalStateManager::class.java, propertyState, "")
        Assert.assertTrue(CloudGlobalStateManager.isInitSuccess())
    }

    @Test
    fun should_correct_when_canShowSyncSwitch() {
        CloudGlobalStateManager.reset()
        Assert.assertTrue(CloudGlobalStateManager.canShowSyncSwitch())

        Whitebox.setInternalState(CloudGlobalStateManager::class.java, propertySupport, false)
        Assert.assertFalse(CloudGlobalStateManager.canShowSyncSwitch())
    }

    @Test
    fun should_correct_when_canDoSyncProcess() {
        CloudGlobalStateManager.reset()
        Whitebox.setInternalState(CloudGlobalStateManager::class.java, propertySupport, false)
        Assert.assertFalse(CloudGlobalStateManager.canDoSyncProcess())

        Whitebox.setInternalState(CloudGlobalStateManager::class.java, propertySupport, true)
        Assert.assertFalse(CloudGlobalStateManager.canDoSyncProcess())

        Whitebox.setInternalState(CloudGlobalStateManager::class.java, propertyState, CloudGlobalStateManager.GLOBAL_OFF)
        Assert.assertTrue(CloudGlobalStateManager.canDoSyncProcess())
    }

    @Test
    fun should_correct_when_reset() {
        CloudGlobalStateManager.reset()
        Assert.assertNull(Whitebox.getInternalState(CloudGlobalStateManager::class.java, propertyState))
    }

    @Test
    fun should_correct_when_handleGlobalOn() {
        Whitebox.invokeMethod<Unit>(CloudGlobalStateManager, "handleGlobalState", FunctionScopeRsp.ENABLE, false)
        Assert.assertEquals(CloudGlobalStateManager.GLOBAL_ON_ENABLE, Whitebox.getInternalState(CloudGlobalStateManager::class.java, propertyState))

        Whitebox.invokeMethod<Unit>(CloudGlobalStateManager, "handleGlobalState", FunctionScopeRsp.ACCOUNT_DISABLED, false)
        Assert.assertEquals(
            CloudGlobalStateManager.GLOBAL_ON_ACCOUNT_DISABLE,
            Whitebox.getInternalState(CloudGlobalStateManager::class.java, propertyState))

        Whitebox.invokeMethod<Unit>(CloudGlobalStateManager, "handleGlobalState", FunctionScopeRsp.DEVICE_DISABLED, false)
        Assert.assertEquals(
            CloudGlobalStateManager.GLOBAL_ON_DEVICE_DISABLE,
            Whitebox.getInternalState(CloudGlobalStateManager::class.java, propertyState))
    }
}