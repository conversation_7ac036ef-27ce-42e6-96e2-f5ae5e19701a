package com.recorder.cloudkit.push;

import android.content.Context;

import com.heytap.cloudkit.libsync.ext.CloudSyncManager;
import com.heytap.cloudkit.libsync.push.CloudPushMessage;
import com.heytap.msp.push.mode.DataMessage;
import com.heytap.msp.push.service.CompatibleDataMessageCallbackService;
import com.soundrecorder.base.utils.DebugUtil;
import com.recorder.cloudkit.SyncTriggerManager;

/**
 * 云同步push message
 * （兼容Q以下版本，需继承CompatibleDataMessageCallbackService）
 */
public class CloudPushServiceBelowQ extends CompatibleDataMessageCallbackService {
    private static final String TAG = "CloudPushServiceBelowQ";

    @Override
    public void processMessage(Context context, DataMessage dataMessage) {
        super.processMessage(context, dataMessage);
        boolean netWorkGranted = com.recorder.cloudkit.utils.CloudPermissionUtils.isNetWorkNoticeGranted(context);
        DebugUtil.e(TAG, "processMessage " + dataMessage.toString() + ", netWorkGranted = " + netWorkGranted);
        if (!netWorkGranted) {
            return;
        }
        String content = dataMessage.getContent();
        boolean isCloudKitMessage = CloudSyncManager.isCloudPushMessage(content);
        if (isCloudKitMessage) {
            CloudPushMessage message = CloudSyncManager.parsePushMessage(content);
            if (null == message) {
                return;
            }
            //收到同步消息
            if (CloudPushMessage.HandleType.SYNC == message.getHandleType()) {
                DebugUtil.e(TAG, "message type == " + CloudPushMessage.HandleType.SYNC);
                SyncTriggerManager.getInstance(context).trigStopSyncForErrorCode(0);
                SyncTriggerManager.getInstance(context).trigRecoveryNow(SyncTriggerManager.RECOVERY_FROM_PUSH);
            }
        }
    }
}
