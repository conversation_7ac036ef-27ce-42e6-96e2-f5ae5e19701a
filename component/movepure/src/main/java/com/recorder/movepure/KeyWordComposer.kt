package com.recorder.movepure

import com.soundrecorder.common.databean.KeyWord
import com.soundrecorder.common.db.KeyWordDbUtils

class KeyWordComposer : BaseXmlComposer<KeyWord>() {

    private val KEY_WORD_XML = "key_word.xml"

    override fun getTag(): String {

        return KeyWordDbUtils.TABLE_KEY_WORD_NAME
    }


    override fun composerData(data: KeyWord) {
        mSerializer.attribute("", KeyWordDbUtils.RECORD_ID, data.recordId.toString())
        mSerializer.attribute("", KeyWordDbUtils.MEDIA_PATH, data.mediaPath)
        mSerializer.attribute("", KeyWordDbUtils.NAME, data.name)
        mSerializer.attribute("", KeyWordDbUtils.TFIDF_VALUE, data.tfidfvalue.toString())
    }

    override fun getXmlName(): String = KEY_WORD_XML
}