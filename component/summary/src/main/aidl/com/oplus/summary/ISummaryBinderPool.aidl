/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * COLOROS_EDIT
 * * File: ISummaryBinderPool.java
 * * Description:摘要入口
 * * Version: 1.0
 * * Date : 2017/11/15
 * * Author: HaiQuan.Li
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>     <date>      <version >    <desc>
 * <EMAIL>   2017/11/15    1.0   create
 ****************************************************************/
package com.oplus.summary;

import android.os.IBinder;
import android.os.Bundle;
import com.oplus.summary.ISummaryBinderPoolCallback;

interface ISummaryBinderPool {
    /**
    * binderCode:通话（预留）call,录音 record,便签-重试 retry
    */
    IBinder queryBinder(String packageName, String binderCode, ISummaryBinderPoolCallback callback);
}
