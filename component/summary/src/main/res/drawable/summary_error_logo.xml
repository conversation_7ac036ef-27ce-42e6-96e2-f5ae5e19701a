<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="280dp"
    android:height="200dp"
    android:viewportWidth="280"
    android:viewportHeight="200">
    <group>
        <clip-path
            android:pathData="M50,24h176.26v151.37h-176.26z"/>
        <path
            android:pathData="M174.64,111.75L178.3,105.08C173.65,100.22 170.93,93.84 170.65,87.13C170.37,80.41 172.54,73.82 176.76,68.59C180.98,63.36 186.96,59.84 193.58,58.7C200.2,57.56 207.02,58.87 212.75,62.38C218.48,65.89 222.73,71.37 224.71,77.8C226.7,84.22 226.27,91.14 223.52,97.27C220.77,103.41 215.88,108.33 209.77,111.12C203.66,113.91 196.74,114.38 190.3,112.44C187.9,111.71 185.61,110.66 183.49,109.32L176.84,113.74C176.56,113.95 176.21,114.06 175.86,114.04C175.51,114.03 175.18,113.89 174.92,113.65C174.66,113.42 174.49,113.1 174.43,112.75C174.38,112.4 174.46,112.05 174.64,111.75Z"
            android:strokeLineJoin="round"
            android:strokeWidth="0.6"
            android:fillColor="#00000000"
            android:fillType="evenOdd"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M202.42,100.95C202.42,101.78 202.17,102.59 201.71,103.29C201.25,103.98 200.59,104.52 199.82,104.84C199.05,105.15 198.21,105.24 197.39,105.08C196.57,104.91 195.82,104.51 195.23,103.92C194.64,103.33 194.24,102.58 194.08,101.77C193.92,100.95 194,100.11 194.32,99.34C194.64,98.57 195.18,97.91 195.87,97.45C196.56,96.98 197.38,96.74 198.21,96.74C198.76,96.74 199.31,96.84 199.82,97.06C200.33,97.27 200.8,97.58 201.19,97.97C201.58,98.36 201.89,98.82 202.1,99.33C202.31,99.85 202.42,100.39 202.42,100.95Z"
            android:fillColor="#8D65AC"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M209.09,79.06C208.89,81.03 208.16,82.91 206.97,84.49C205.82,86.05 204.27,87.29 202.48,88.06C202.19,88.19 201.96,88.41 201.8,88.68C201.62,88.94 201.53,89.26 201.53,89.58V90.71C201.55,91.15 201.49,91.59 201.34,92.01C201.18,92.43 200.95,92.81 200.64,93.13C200.34,93.45 199.97,93.71 199.56,93.88C199.16,94.06 198.72,94.15 198.27,94.15C197.83,94.15 197.39,94.06 196.99,93.88C196.58,93.71 196.21,93.45 195.91,93.13C195.6,92.81 195.37,92.43 195.21,92.01C195.06,91.59 194.99,91.15 195.02,90.71V89.71C194.97,88.12 195.41,86.56 196.27,85.23C197.11,83.84 198.35,82.74 199.82,82.06H199.88C200.61,81.77 201.25,81.27 201.7,80.63C202.17,80 202.47,79.26 202.56,78.48C202.63,77.7 202.49,76.91 202.15,76.21C201.82,75.49 201.3,74.88 200.65,74.44C199.99,74 199.23,73.75 198.44,73.73C197.66,73.67 196.88,73.85 196.2,74.23C195.49,74.58 194.9,75.14 194.5,75.82C194.13,76.5 193.95,77.28 193.97,78.06C193.97,78.92 193.63,79.76 193.01,80.37C192.4,80.98 191.57,81.33 190.7,81.33C189.83,81.33 189,80.98 188.39,80.37C187.77,79.76 187.43,78.92 187.43,78.06C187.43,76.09 187.97,74.15 188.98,72.46C190,70.78 191.45,69.39 193.19,68.47C194.49,67.77 195.92,67.35 197.39,67.22C198.86,67.1 200.34,67.28 201.74,67.75C203.14,68.21 204.43,68.96 205.52,69.95C206.62,70.93 207.51,72.13 208.13,73.47C208.95,75.21 209.28,77.14 209.09,79.06Z"
            android:fillColor="#8D65AC"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M69.78,148.37L59.84,155.08L59.65,155.16L52.55,157C52.03,157.13 51.55,157.37 51.15,157.72C50.75,158.07 50.44,158.51 50.24,159C50.21,159.09 50.19,159.18 50.21,159.28C50.22,159.37 50.25,159.46 50.3,159.54C50.36,159.62 50.43,159.68 50.51,159.73C50.59,159.77 50.69,159.8 50.78,159.8H71"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M100.97,143.74L113.53,142.44"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M124,85.44L124.02,85.4L126.25,86.43L126.68,87.36C126.59,91.12 126.49,95.41 126.35,100.22C125.85,117.65 120,116.65 120,116.65L119.78,118.84C119.76,119.08 119.67,119.32 119.54,119.52C119.41,119.72 119.23,119.89 119.02,120.01C118.81,120.14 118.57,120.2 118.32,120.21C118.08,120.22 117.84,120.17 117.62,120.07C115.51,118.96 113.43,117.77 111.41,116.51L95.35,97.82C95.39,97.06 95.47,96.24 95.57,95.34C95.7,94.21 95.98,92.84 96.35,91.3L113.19,91.57C113.83,91.61 114.48,91.52 115.09,91.3C115.5,91.16 115.87,90.96 116.22,90.72C116.62,90.59 117,90.39 117.34,90.13C118.84,88.97 121.82,84.83 121.85,84.8L124,85.44ZM126.74,84.35C126.74,84.37 126.74,84.39 126.74,84.41L126.38,84.21L126.74,84.35ZM125.6,49.68C120.98,52.81 116.41,58.49 116.37,58.54C116.42,58.54 120.85,58.59 122.06,60.21C123.71,62.43 126.45,72.52 126.93,74.31C126.92,75.08 126.9,75.91 126.89,76.79L126.74,76.75C126.51,76.64 126.26,76.57 126.01,76.54L123.28,75.8C123.03,75.75 122.77,75.77 122.52,75.83C122.28,75.9 122.05,76.02 121.86,76.19L114.35,81.53C114.12,81.47 113.89,81.44 113.65,81.42L99.74,79.62C101.52,73.73 103.14,67.99 102.98,64.98C102.94,64.05 102.85,61.93 102.85,61.11C102.71,57 104.63,54.25 108.71,52.93C112.73,51.63 122.34,50.16 125.6,49.68Z"
            android:fillColor="#5966A9"/>
        <path
            android:pathData="M88.05,147.21L88.61,160.17"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M149.29,54.17C149.97,54.31 150.64,54.53 151.28,54.81C152.64,55.39 153.48,57.62 154.01,59.02C154.03,59.08 154.05,59.13 154.07,59.19C155.09,61.84 155.48,64.68 155.23,67.5C155.02,69.72 151.57,85.17 150.05,91.93C149.66,93.69 149.4,94.86 149.36,95.06C149.09,96.39 149.23,100.56 149.36,104.3C149.44,106.59 149.51,108.73 149.48,109.96C149.4,113.55 147.4,114.67 146.08,115.4C145.6,115.67 145.22,115.89 145.04,116.15C144.82,116.49 144.47,117.49 144.1,118.6C143.58,120.14 142.99,121.87 142.57,122.21C136.64,127 130.15,128.21 128,127.89C127.28,127.79 125.21,121.14 128.16,118.11C133.23,112.9 133.46,105.77 133.56,99.43C133.63,95.86 134.11,92.31 135,88.85C135.65,86.29 136.44,83.77 137.3,81.26C137.28,81.33 137.26,81.4 137.25,81.46C137.35,81.35 137.48,81.2 137.65,81C139.96,78.35 148.62,68.41 149,62.33C149.11,60.33 154.68,63.98 154.68,63.98C154.68,63.98 151.65,58.27 149.15,54.39L149.29,54.17Z"
            android:fillColor="#5966A9"/>
        <path
            android:pathData="M151.46,55.15C156.75,55.15 161.04,59.99 161.04,65.96C161.04,71.93 156.75,76.77 151.46,76.77C148.5,76.77 145.85,75.26 144.1,72.88C146.58,69.32 148.81,65.35 149,62.33C149.11,60.34 154.65,63.96 154.68,63.98C154.68,63.98 152.11,59.12 149.74,55.32C150.3,55.21 150.87,55.15 151.46,55.15ZM142.01,66.35C141.97,66.46 141.94,66.58 141.9,66.7C141.89,66.53 141.88,66.35 141.88,66.18L142.01,66.35Z"
            android:fillColor="#5966A9"/>
        <path
            android:pathData="M143.15,59.24C143.27,60.33 143.22,61.45 143.04,62.52C142.55,65.38 137.94,78.65 137.25,81.46C138.66,79.81 148.59,68.85 149,62.33C149.11,60.33 154.68,63.98 154.68,63.98C154.68,63.98 151.66,58.28 149.15,54.39"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M129.88,47.7C129.76,47.73 129.65,47.76 129.53,47.79C124,49.05 116.37,58.54 116.37,58.54C116.37,58.54 120.84,58.58 122.06,60.21C123.87,62.65 127,74.58 127,74.58C127.55,70.58 127.47,66.51 126.76,62.53C126.17,60.48 126.28,58.29 127.06,56.3C127.52,55.2 128.05,54.27 128.81,53.64"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M116.15,67.58C118.11,61.94 115.65,55.96 110.65,54.22C105.66,52.48 100.02,55.64 98.05,61.28C96.09,66.92 98.55,72.9 103.54,74.64C108.54,76.38 114.18,73.22 116.15,67.58Z"
            android:fillColor="#5966A9"/>
        <path
            android:pathData="M72.43,149.9L73.47,154.09L70,162.31H69.7C69.27,162.34 68.87,162.54 68.58,162.85C68.29,163.16 68.13,163.58 68.13,164.01C68.13,164.43 68.29,164.85 68.58,165.16C68.87,165.47 69.27,165.67 69.7,165.7H85.6C86.05,165.7 86.48,165.52 86.8,165.2C87.12,164.88 87.3,164.45 87.3,164C87.29,163.77 87.24,163.55 87.15,163.35C87.05,163.14 86.91,162.96 86.74,162.81V161.81C86.74,160.37 86.51,158.94 86.08,157.57L84.52,152.66L84.1,149.9"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M153.01,42.11C152.73,43.88 152.46,45.65 152.05,47.4C153.61,45.57 154.65,43.27 154.87,40.68C154.89,40.46 154.91,40.23 154.94,40.01L154.94,40.01L154.94,40.01C154.98,39.6 155.03,39.2 155.03,38.8C155.7,30.91 149.66,25.21 141.97,24.8H141.59C133.9,24.25 127.18,28.54 126.54,36.23C126.54,36.23 126.37,37.65 126.37,38.14C126.47,43.02 127.95,46.98 130.28,49.52C130.4,49.11 130.51,48.69 130.62,48.28C130.11,47.93 129.66,47.5 129.3,47C128.2,45.58 127.3,42.94 128.56,41.7C129.05,41.3 129.66,41.07 130.29,41.04C130.93,41.02 131.55,41.2 132.07,41.56C132.66,42.01 133.07,42.66 133.23,43.39L134.73,35.34C134.78,34.67 134.96,34.02 135.27,33.43C135.58,32.84 136.01,32.32 136.53,31.9C137.05,31.48 137.65,31.17 138.29,30.99C138.94,30.81 139.61,30.77 140.27,30.86C140.4,30.88 140.52,30.91 140.64,30.95L149.64,32.58L150.02,32.63C150.67,32.77 151.29,33.05 151.83,33.45C152.37,33.85 152.82,34.35 153.15,34.93C153.48,35.51 153.69,36.15 153.76,36.82C153.83,37.48 153.76,38.15 153.56,38.79C153.36,39.89 153.18,41 153.01,42.11L153.01,42.11Z"
            android:fillColor="#525559"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M130.6,48.32C130.17,50.05 129.63,51.76 128.99,53.43C127.76,54.8 127.08,56.59 127.09,58.43C127.09,60.43 127.88,62.34 129.29,63.75C130.71,65.17 132.62,65.96 134.62,65.96C136.62,65.96 138.53,65.17 139.94,63.75C141.12,62.58 141.87,61.06 142.08,59.43"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M153.56,38.79C152.95,42.07 152.62,45.43 151.73,48.66C150.07,54.66 145.53,60.03 141.07,59.3C140.84,59.27 140.61,59.21 140.39,59.13C138.04,58.58 134.96,56.13 133.11,52.91C132.33,51.56 131.7,50.13 131.23,48.64C130.46,48.26 129.8,47.69 129.3,47C128.2,45.58 127.3,42.94 128.56,41.7C129.05,41.3 129.66,41.07 130.29,41.04C130.93,41.02 131.55,41.2 132.07,41.56C132.66,42.01 133.07,42.66 133.23,43.39L134.73,35.34C134.78,34.67 134.96,34.02 135.27,33.43C135.58,32.84 136.01,32.32 136.53,31.9C137.05,31.48 137.65,31.17 138.29,30.99C138.93,30.81 139.61,30.77 140.27,30.86C140.4,30.88 140.52,30.91 140.64,30.95L149.64,32.58L150.02,32.63C150.67,32.77 151.29,33.05 151.83,33.45C152.37,33.85 152.82,34.35 153.15,34.93C153.48,35.51 153.69,36.15 153.76,36.82C153.83,37.48 153.76,38.15 153.56,38.79Z"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M132.59,40.24C132.59,40.24 134.65,40.24 136.98,37.89C141.52,33.36 141,29.31 141,29.31C141,29.31 136.36,28.92 134.21,31.48C131.87,34.34 132.59,40.24 132.59,40.24Z"
            android:fillColor="#525559"/>
        <path
            android:pathData="M139.25,30.44C139.98,32.87 141.5,34.98 143.58,36.44C145.12,37.48 146.88,38.12 148.73,38.31L147.5,33.89L153.5,41.32C153.5,41.32 156.65,31.12 145.71,28.88C142.37,28.2 139.25,30.44 139.25,30.44Z"
            android:fillColor="#525559"/>
        <path
            android:pathData="M128.72,43.77C128.92,43.57 129.19,43.44 129.47,43.41C129.76,43.37 130.05,43.44 130.29,43.6"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M140.78,45.15C140.78,45.75 140.36,46.21 139.96,46.15C139.56,46.09 139.28,45.59 139.33,44.99C139.38,44.39 139.75,43.94 140.15,43.99C140.55,44.04 140.83,44.55 140.78,45.15Z"
            android:fillColor="#323739"/>
        <path
            android:pathData="M158.89,129.16H158.85H146.38L145.25,73.42L145.12,73.01L161,64.94L163.51,129.64L158.89,129.17L158.89,129.16Z"
            android:fillColor="#5966A9"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M143.2,50.39C143.49,50.56 143.8,50.68 144.13,50.72C144.47,50.75 144.8,50.72 145.12,50.62"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#323739"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M138.78,43.1C138.99,42.84 139.27,42.64 139.57,42.51C139.88,42.38 140.22,42.33 140.55,42.35C141.04,42.42 141.5,42.67 141.82,43.05"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#323739"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M147.59,46C147.5,46.55 147.74,47 148.11,47.1C148.48,47.2 148.86,46.76 148.95,46.22C149.04,45.68 148.8,45.22 148.43,45.12C148.06,45.02 147.68,45.41 147.59,46Z"
            android:fillColor="#323739"/>
        <path
            android:pathData="M150.46,43.82C150.14,43.51 149.72,43.33 149.28,43.3C148.78,43.28 148.29,43.44 147.91,43.76"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#323739"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M144.31,53.74C143.38,53.77 142.46,53.62 141.59,53.31C140.82,53.03 140.1,52.63 139.45,52.12"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#323739"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M136.34,39.94C137.56,39.28 138.96,39.02 140.34,39.2C141.62,39.37 142.81,39.92 143.77,40.78"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#323739"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M147.39,41.07C148.18,40.46 149.17,40.18 150.16,40.28C150.8,40.35 151.4,40.6 151.91,40.99"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#323739"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M133.83,39.21C133.93,40.1 133.93,40.99 133.83,41.88C133.74,42.56 133.57,43.23 133.33,43.88L132.42,39.8L133.83,39.21Z"
            android:fillColor="#525559"/>
        <path
            android:pathData="M131.53,46.47L133.01,47.79C133.07,47.85 133.15,47.88 133.24,47.87C133.32,47.87 133.4,47.83 133.46,47.77L133.76,47.42C133.82,47.36 133.85,47.28 133.85,47.19C133.84,47.11 133.8,47.03 133.74,46.97L132.38,45.76"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M132.26,43.65C132.53,43.98 132.67,44.4 132.66,44.83C132.65,45.26 132.49,45.67 132.21,45.99C131.92,46.31 131.53,46.51 131.11,46.57C130.68,46.63 130.25,46.53 129.89,46.3C129.62,45.97 129.48,45.55 129.49,45.12C129.5,44.69 129.66,44.28 129.95,43.96C130.23,43.64 130.62,43.44 131.04,43.38C131.47,43.32 131.9,43.42 132.26,43.65Z"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M142.15,59.25C142.02,60.32 141.65,61.34 141.07,62.25C139.24,61.52 137.62,60.36 136.34,58.87C135.05,57.38 134.15,55.6 133.71,53.68C135.58,56.41 138.29,58.48 140.41,58.98C140.63,59.06 140.86,59.12 141.09,59.15C141.44,59.22 141.79,59.26 142.15,59.25Z"
            android:fillColor="#808080"/>
        <path
            android:pathData="M101.86,105.31C107.32,104.89 112.68,103.44 117.64,101C115.75,104.06 113.11,106.59 109.96,108.34C108.69,109.04 107.36,109.61 105.98,110.04"
            android:strokeLineJoin="round"
            android:strokeWidth="0.41"
            android:fillColor="#00000000"
            android:strokeColor="#525559"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M142.26,75.39C137.41,87.45 136.1,95.1 136.07,108.11C136.07,109.85 135.92,111.58 135.77,113.32C135.4,117.65 131.77,120.13 131.4,121.14C129.97,124.63 130.8,126.71 131.4,127.62"
            android:strokeLineJoin="round"
            android:strokeWidth="0.41"
            android:fillColor="#00000000"
            android:strokeColor="#525559"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M154.08,59.19C155.1,61.83 155.49,64.68 155.24,67.5C154.98,70.3 149.56,94.1 149.37,95.06C148.94,97.21 149.57,106.73 149.49,109.96C149.38,114.84 145.72,115.15 145.05,116.15C144.51,116.96 143.3,121.62 142.58,122.21"
            android:strokeLineJoin="round"
            android:strokeWidth="0.41"
            android:fillColor="#00000000"
            android:strokeColor="#525559"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M127.97,84.83L130.3,89.46C130.39,89.63 130.54,89.77 130.73,89.85C130.91,89.92 131.11,89.94 131.3,89.88C131.5,89.81 131.66,89.66 131.76,89.48C131.84,89.55 131.93,89.62 132.03,89.67C132.04,89.67 132.05,89.67 132.07,89.68L125.32,98.79C125.19,98.97 125.02,99.13 124.83,99.24C124.64,99.36 124.43,99.44 124.2,99.47C123.98,99.51 123.75,99.5 123.54,99.44C123.32,99.39 123.11,99.29 122.93,99.16L116.05,94.06C115.7,93.8 115.46,93.41 115.38,92.98C115.31,92.55 115.4,92.11 115.64,91.74C115.64,91.71 115.64,91.69 115.64,91.66L116.38,90.66C116.72,90.53 117.05,90.36 117.34,90.13C118.85,88.97 121.83,84.83 121.85,84.8L124,85.44L124.03,85.4L126.25,86.43L127.75,89.69C127.84,89.87 127.99,90.01 128.17,90.1C128.35,90.18 128.56,90.19 128.75,90.13C128.86,90.1 128.97,90.04 129.06,89.96C129.15,89.88 129.22,89.78 129.27,89.67C129.32,89.57 129.35,89.45 129.35,89.33C129.35,89.21 129.33,89.09 129.28,88.98L127.88,85.24C127.8,85.05 127.66,84.9 127.48,84.79H127.42L126.38,84.21L127.97,84.83ZM130.59,80.47L135.27,84.27C135.43,84.42 135.64,84.5 135.85,84.5C135.87,84.5 135.89,84.49 135.91,84.49L133.26,88.07L131.18,81.33C131.09,81.06 130.91,80.82 130.66,80.68L130.54,80.61L130.17,80.4L130.59,80.47ZM130.06,73.11C130.51,73.04 130.96,73.16 131.33,73.43L138.21,78.52C138.39,78.65 138.54,78.82 138.66,79.01C138.77,79.21 138.85,79.42 138.88,79.64C138.92,79.86 138.9,80.09 138.85,80.31C138.79,80.53 138.7,80.73 138.56,80.91L136.69,83.43C136.68,83.38 136.67,83.34 136.65,83.3C136.61,83.19 136.54,83.08 136.46,83L131.96,78.35C131.85,78.23 131.71,78.14 131.55,78.1H131.39L126.74,76.75C126.74,76.75 126.73,76.75 126.72,76.74L128.93,73.78C129.2,73.42 129.61,73.18 130.06,73.11Z"
            android:fillColor="#525559"/>
        <path
            android:pathData="M131.88,89.32L129.26,82.91C129.19,82.73 129.05,82.59 128.88,82.51L125.4,80.87"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M130.22,80.46L126.77,78.95"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M126.06,84.1L124.26,83.32"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M73.14,75.04C77.06,71.23 83.32,71.33 87.12,75.25C90.73,78.97 90.83,84.8 87.47,88.64L82.34,82.66C82.2,82.5 82.05,82.34 81.9,82.19C81.33,81.5 80.68,80.93 79.85,80.34C78.56,79.42 77.03,78.78 75.49,78.42C74.15,78.11 72.37,78.14 70.9,78.33C71.4,77.12 72.14,76 73.14,75.04Z"
            android:fillColor="#5966A9"/>
        <path
            android:pathData="M112.3,73.97L87.76,88.97L82.34,82.66C81.17,81.31 79.73,80.24 78.13,79.51L74.25,74.11L102.35,55.51L112.3,73.97Z"
            android:fillColor="#5966A9"/>
        <path
            android:pathData="M108.05,78.12C108.31,78.17 108.56,78.26 108.78,78.41C109,78.55 109.2,78.74 109.35,78.96C109.5,79.18 109.6,79.42 109.65,79.68C109.71,79.94 109.71,80.21 109.66,80.47L107.66,92.09C107.59,92.59 107.32,93.04 106.93,93.36C106.54,93.67 106.03,93.83 105.53,93.79L91.24,93.02L82.34,82.66C81.87,82.12 81.36,81.63 80.82,81.19L82.11,72.85L108.05,78.12Z"
            android:fillColor="#5966A9"/>
        <path
            android:pathData="M112.16,79.99L109.99,79.71C109.36,79.63 108.79,80.08 108.71,80.71L107.27,92.14C107.19,92.77 107.64,93.35 108.27,93.43L110.43,93.7C111.06,93.78 111.64,93.33 111.72,92.7L113.15,81.27C113.23,80.64 112.79,80.06 112.16,79.99Z"
            android:fillColor="#5966A9"/>
        <path
            android:pathData="M82.11,72.86L108,78.12C108.4,78.2 108.76,78.39 109.05,78.68C109.33,78.97 109.53,79.33 109.61,79.73C109.72,79.72 109.84,79.72 109.95,79.73L112.11,80C112.41,80.04 112.69,80.2 112.88,80.44C113.06,80.68 113.15,80.99 113.11,81.29L111.71,92.71C111.67,93.01 111.51,93.29 111.27,93.48C111.03,93.66 110.72,93.75 110.42,93.71L108.27,93.44C108.08,93.42 107.91,93.35 107.76,93.25C107.61,93.14 107.48,93 107.4,92.83C107.21,93.15 106.94,93.42 106.62,93.59C106.29,93.77 105.92,93.85 105.55,93.83L95.87,93.31"
            android:strokeLineJoin="round"
            android:strokeWidth="0.41"
            android:fillColor="#00000000"
            android:strokeColor="#525559"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M109.5,81.44L107.68,92.09"
            android:strokeLineJoin="round"
            android:strokeWidth="0.41"
            android:fillColor="#00000000"
            android:strokeColor="#525559"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M108.79,54.4C113.79,56.14 116.25,62.12 114.28,67.75C113.38,69.84 112.08,71.74 110.45,73.34L105.45,77.51"
            android:strokeLineJoin="round"
            android:strokeWidth="0.41"
            android:fillColor="#00000000"
            android:strokeColor="#525559"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M86.87,130.34L84.26,90.18"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M70.61,145.8L85.57,145.51"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M153.24,38.12C153.94,38.17 154.61,38.41 155.18,38.81C155.75,39.21 156.21,39.75 156.5,40.39C157.18,34.76 156.76,32.24 153.74,29.57C154.22,29.28 154.62,28.87 154.89,28.39C155.16,27.9 155.31,27.35 155.31,26.79C153.22,27.39 151,27.25 149,26.4C147.04,25.56 145.04,24.83 143,24.23C141.97,23.94 140.89,23.88 139.83,24.07C138.78,24.26 137.78,24.69 136.92,25.33C136.92,26.19 137,27.07 137.05,27.93C140.35,28.28 143.52,29.38 146.33,31.14C149.14,32.91 151.5,35.3 153.24,38.12Z"
            android:fillColor="#525559"/>
        <path
            android:pathData="M84.21,149.8H85.56C85.84,149.8 86.12,149.69 86.32,149.49C86.52,149.29 86.64,149.01 86.64,148.73V146.6"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M113.4,81.46L114.28,81.57L121.85,76.14C122.04,75.97 122.27,75.85 122.52,75.78C122.77,75.71 123.03,75.7 123.28,75.75L126,76.49C126.25,76.53 126.49,76.62 126.72,76.74L131.43,78.1H131.59C131.74,78.15 131.88,78.24 131.99,78.35L136.46,83C136.54,83.08 136.61,83.18 136.65,83.29C136.7,83.39 136.72,83.51 136.72,83.62C136.72,83.74 136.7,83.86 136.65,83.96C136.61,84.07 136.54,84.17 136.46,84.25C136.3,84.39 136.09,84.47 135.87,84.47C135.66,84.47 135.45,84.39 135.29,84.25L130.62,80.45L130.25,80.39L130.52,80.54L130.63,80.6C130.88,80.74 131.06,80.97 131.15,81.25L133.37,88.46C133.43,88.7 133.4,88.96 133.29,89.18C133.17,89.4 132.97,89.57 132.74,89.65C132.57,89.7 132.4,89.71 132.23,89.67C132.06,89.62 131.91,89.53 131.79,89.41C131.69,89.6 131.53,89.74 131.33,89.81C131.14,89.87 130.94,89.86 130.75,89.78C130.57,89.7 130.42,89.56 130.33,89.39L127.99,84.76L126.21,84.06L127.45,84.75H127.51C127.68,84.86 127.82,85.02 127.91,85.21L129.23,89C129.31,89.21 129.3,89.44 129.21,89.64C129.12,89.84 128.95,90 128.74,90.08C128.55,90.14 128.34,90.13 128.16,90.05C127.97,89.97 127.83,89.83 127.74,89.65L126.24,86.39L124.05,85.39L121.89,84.76C121.89,84.76 118.89,88.93 117.39,90.09C117.05,90.36 116.66,90.56 116.25,90.7C115.37,91.31 114.31,91.6 113.25,91.52H112.11"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M86.63,160.16H88.39C88.83,160.62 89.43,160.9 90.07,160.94L119.4,162.37M142.82,122.22L143.46,127.11C144.03,127.44 144.59,127.81 145.11,128.21C145.49,128.5 145.86,128.81 146.21,129.13M114.22,119.8L82.34,82.66C81,81.01 79.27,79.72 77.29,78.93C75.32,78.14 73.17,77.87 71.07,78.15C68.96,78.43 66.96,79.24 65.25,80.51C63.55,81.79 62.2,83.48 61.34,85.42C60.29,87.64 59.94,90.12 60.34,92.54L67.53,143.29C67.62,143.94 67.88,144.56 68.27,145.09C68.67,145.61 69.2,146.02 69.8,146.29V148.42C69.8,148.79 69.95,149.14 70.2,149.4C70.46,149.65 70.81,149.8 71.18,149.8H84.18M90.18,144.8C89.7,144.85 89.24,145.04 88.86,145.35C88.49,145.66 88.22,146.08 88.09,146.55H86.64V144.76C86.64,144.72 86.64,144.68 86.64,144.64C86.99,144.26 87.26,143.8 87.43,143.31C87.61,142.82 87.68,142.3 87.64,141.78L86.87,130.29L91.87,136.98L92.4,137.7C94.58,140.45 97.71,142.28 101.17,142.84C101.56,143.05 101.96,143.23 102.38,143.38L102.78,143.51L90.18,144.8Z"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M119.78,118.84C119.78,118.84 120.78,119.07 122.99,119.55C124.73,119.91 126.92,120.21 126.92,120.21"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M119.45,129.16H158.89L158.85,175.17L121.85,169C121.21,169 120.61,168.75 120.16,168.3C119.71,167.85 119.45,167.24 119.45,166.61V147.06"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M191.04,147.92H184.7V131.77L191.04,147.92Z"
            android:fillColor="#525559"/>
        <path
            android:pathData="M182.48,167.22L158.85,175.17V129.17L184.7,131.78V164.84C184.7,165.44 184.47,166.02 184.05,166.46C183.64,166.9 183.08,167.18 182.48,167.22Z"
            android:strokeLineJoin="round"
            android:strokeWidth="0.33"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M170.64,171.46V151.02L175.96,150.25V169.41L170.64,171.46Z"
            android:fillColor="#A3AACC"/>
        <path
            android:pathData="M152.67,149.61L111.84,146.39L119.41,129.16H158.85L152.67,149.61Z"
            android:strokeLineJoin="round"
            android:strokeWidth="0.4"
            android:fillColor="#00000000"
            android:strokeColor="#808080"
            android:strokeLineCap="round"/>
    </group>
</vector>
