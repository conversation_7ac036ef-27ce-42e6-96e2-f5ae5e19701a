<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:forceDarkAllowed="false">

    <com.soundrecorder.common.widget.OSImageView
        android:id="@+id/error_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:forceDarkAllowed="false"
        app:anim_raw_json="@raw/summary_error_logo_json"
        app:anim_raw_json_night="@raw/summary_error_logo_json_night"
        app:img_draw="@drawable/summary_error_logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/error_msg_text"
        style="@style/couiTextHeadlineXS"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:gravity="center"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textFontWeight="500"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/error_logo" />

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/retry"
        style="@style/couiTextButtonM"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp12"
        android:paddingVertical="@dimen/dp4"
        android:text="@string/retry"
        android:textColor="?attr/couiColorLabelTheme"
        android:textFontWeight="500"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/error_msg_text" />

</androidx.constraintlayout.widget.ConstraintLayout>