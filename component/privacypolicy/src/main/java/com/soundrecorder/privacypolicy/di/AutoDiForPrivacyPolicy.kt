/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForPrivacyPolicy.kt
 * * Description : AutoDiForPrivacyPolicy
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.privacypolicy.di

import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface
import com.soundrecorder.privacypolicy.PrivacyPolicyApi
import org.koin.dsl.module

object AutoDiForPrivacyPolicy {
    val privacyPolicyModule = module {
        single<PrivacyPolicyInterface>(createdAtStart = true) {
            PrivacyPolicyApi
        }
    }
}