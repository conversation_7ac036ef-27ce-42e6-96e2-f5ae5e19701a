/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ShadowFeatureOption
 Description:
 Version: 1.0
 Date: 2023/05/25 1.0
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2023/05/25 1.0 create
 */

package com.soundrecorder.privacypolicy.shadows;

import android.content.Context;

import com.soundrecorder.base.utils.FeatureOption;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(FeatureOption.class)
public class ShadowFeatureOption {

    @Implementation
    public static void loadOptions(Context context) {
    }

    @Implementation
    public static boolean loadSpeechToTextFeature() {
        return true;
    }

    @Implementation
    public static boolean isOpenPhotoRecommendFeature() { return true; }

    @Implementation
    public static boolean isTablet() {
        return false;
    }

}
