package com.soundrecorder.convertservice.convert;

import android.content.Context;
import android.os.Build;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import com.soundrecorder.convertservice.process.ProcessConvert;
import com.soundrecorder.convertservice.process.ProcessSendOCS;
import com.soundrecorder.convertservice.shadows.ShadowFeatureOption;
import com.soundrecorder.convertservice.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.databean.ConvertStatus;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class NewConvertTextRunnableTest {

    private static final long MEDIA_ID_0 = 0L;
    private static final String M_PROCESS_CONVERT = "mProcessConvert";
    private static final String M_PROCESS_SENDOCS = "mProcessSendOCS";
    private static final String M_FUNCTION_DO_RUN = "doRun";

    private Context mContext;
    private NewConvertTextRunnable mRunnable;
    private ProcessConvert mockProcessConvert;
    private ProcessSendOCS mockProcessSendOCS;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        mRunnable = new NewConvertTextRunnable(MEDIA_ID_0, false);
        System.setProperty("javax.net.ssl.trustStore", "NONE");
        mockProcessConvert = new ProcessConvert(null, null, false);
        mockProcessSendOCS = new ProcessSendOCS(mockProcessConvert, null, false);
    }

    @After
    public void tearDown() {
        mContext = null;
        mRunnable.release();
        mRunnable = null;
        mockProcessConvert = null;
        mockProcessSendOCS = null;
    }

    @Test
    public void should_not_null_when_registerConvertUiCallback() throws Exception {
        IConvertCallback callback = Mockito.mock(IConvertCallback.class);
        mockProcessConvert.setConvertCallback(callback);
        mockProcessSendOCS.setConvertCallback(callback);
        Whitebox.invokeMethod(mRunnable, M_FUNCTION_DO_RUN);
        ProcessConvert processConvert = Whitebox.getInternalState(mRunnable, M_PROCESS_CONVERT);
        ProcessSendOCS processSendOCS = Whitebox.getInternalState(mRunnable, M_PROCESS_SENDOCS);
        Assert.assertNotNull(processConvert);
        Assert.assertNotNull(processSendOCS);
        mRunnable.registerConvertUiCallback(callback);
        Assert.assertEquals(mockProcessConvert.getConvertCallback(), processConvert.getConvertCallback());
        Assert.assertEquals(mockProcessSendOCS.getConvertCallback(), processSendOCS.getConvertCallback());
    }

    @Test
    public void should_null_when_unregisterConvertUiCallback() throws Exception {
        IConvertCallback callback = Mockito.mock(IConvertCallback.class);
        mRunnable.setMConvertUiCallback(callback);
        Whitebox.invokeMethod(mRunnable, M_FUNCTION_DO_RUN);
        ProcessConvert processConvert = Whitebox.getInternalState(mRunnable, M_PROCESS_CONVERT);
        ProcessSendOCS processSendOCS = Whitebox.getInternalState(mRunnable, M_PROCESS_SENDOCS);
        Assert.assertNotNull(mRunnable.getMConvertUiCallback());
        Assert.assertNotNull(processConvert.getConvertCallback());
        Assert.assertNotNull(processSendOCS.getConvertCallback());
        mRunnable.unregisterConvertUiCallback();
        Assert.assertNull(mRunnable.getMConvertUiCallback());
        Assert.assertNull(processConvert.getConvertCallback());
        Assert.assertNull(processSendOCS.getConvertCallback());
    }

    @Test
    public void should_cancel_when_release() throws Exception {
        Whitebox.invokeMethod(mRunnable, M_FUNCTION_DO_RUN);
        ProcessConvert processConvert = Whitebox.getInternalState(mRunnable, M_PROCESS_CONVERT);
        ProcessSendOCS processSendOCS = Whitebox.getInternalState(mRunnable, M_PROCESS_SENDOCS);
        Assert.assertFalse(processConvert.getMIsAbort());
        Assert.assertFalse(processSendOCS.getMIsAbort());
        mRunnable.release();
        Assert.assertTrue(processConvert.getMIsAbort());
        Assert.assertTrue(processSendOCS.getMIsAbort());
    }

    @Test
    public void should_return_convert_status_when_getCurrentConvertStatus() {
        mRunnable.getMConvertRecord().setUploadStatus(ConvertStatus.UPLOAD_STATUS_UPLOADING);
        mRunnable.getMConvertRecord().setConvertStatus(ConvertStatus.CONVERT_STATUS_UNINIT);
        ConvertStatus actualConvertStatus = mRunnable.getCurrentConvertStatus();
        Assert.assertNotNull(actualConvertStatus);
        Assert.assertEquals(ConvertStatus.UPLOAD_STATUS_UPLOADING, actualConvertStatus.getUploadStatus());
        Assert.assertEquals(ConvertStatus.CONVERT_STATUS_UNINIT, actualConvertStatus.getConvertStatus());
    }

    @Test
    public void should_not_null_when_start() {
//        IConvertCallback callback = Mockito.mock(IConvertCallback.class);
//        mRunnable.setMConvertUiCallback(callback);
//        mRunnable.start();
//        ShadowLooper.runUiThreadTasks();
//        ProcessConvert processConvert = Whitebox.getInternalState(mRunnable, M_PROCESS_CONVERT);
//        ProcessSendOCS processSendOCS = Whitebox.getInternalState(mRunnable, M_PROCESS_SENDOCS);
//        Assert.assertNotNull(processConvert);
//        Assert.assertNotNull(processSendOCS);
//        Assert.assertNotNull(processConvert.getConvertCallback());
//        Assert.assertNotNull(processSendOCS.getConvertCallback());
    }
}
