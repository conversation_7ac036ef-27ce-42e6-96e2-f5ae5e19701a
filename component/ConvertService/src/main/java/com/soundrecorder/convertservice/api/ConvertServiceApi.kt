/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  ConvertServiceApi.kt
 * * Description : ConvertServiceApi
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.convertservice.api

import android.content.Context
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.convertservice.convert.NewConvertResultUtil
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.modulerouter.convertService.ConvertServiceInterface

object ConvertServiceApi : ConvertServiceInterface {

    private const val TAG = "ConvertServiceApi"

    override fun getConvertSavePath(context: Context): String {
        return NewConvertResultUtil.getConvertSavePath(context)
    }

    override fun getConvertFileNameWithPostFix(recordId: Long, title: String): String? {
        return NewConvertResultUtil.genFileName(recordId, title)
    }

    override fun getConvertFilePath(context: Context, fileName: String): String? {
        return NewConvertResultUtil.genConvertTextPath(context, fileName)
    }

    override fun <T> writeConvertContent(filePath: String?, dataList: List<T>?) {
        ConvertToUtils.reWriteConvertFile(filePath, dataList as? List<ConvertContentItem>)
    }
}