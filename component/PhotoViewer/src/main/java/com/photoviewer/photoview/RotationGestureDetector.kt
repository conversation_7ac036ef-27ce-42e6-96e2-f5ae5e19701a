package com.photoviewer.photoview

import android.os.SystemClock
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.pow
import kotlin.math.sqrt

class RotationGestureDetector(
    private val rotationCallback: (Float) -> Unit,
    private val rotationEnd: (Float) -> Unit
) {
    companion object {
        private const val CHECK_ROTATION = 3f
        private const val CHECK_TIME = 100
        private const val STATE_CHECK_INIT = 0
        private const val STATE_CHECK_DOING = 1
        private const val STATE_CHECK_TROUGH = 2
        private const val STATE_CHECK_NOT_TROUGH = 3
        private const val ROTATION_RATIO = 1.3f
    }

    private val empty = floatArrayOf()
    private var prevData = empty
    private var currData = empty
    private var checkState = STATE_CHECK_INIT
    private var checkStartTime = 0L
    var lastEndRotation = 0f
    fun onTouchEvent(view: View, ev: MotionEvent) {
        if (ev.action == MotionEvent.ACTION_DOWN) {
            resetPrevData()
        }
        if (ev.pointerCount == 2) {
            this.currData = floatArrayOf(
                ev.getX(0),
                ev.getY(0),
                ev.getX(1),
                ev.getY(1),
            )
            if (this.prevData.contentEquals(empty)) {
                this.prevData = currData
            }
            if (checkState == STATE_CHECK_INIT) {
                checkStartTime = SystemClock.elapsedRealtime()
                checkState = STATE_CHECK_DOING
            } else if (checkState == STATE_CHECK_DOING) {
                if (SystemClock.elapsedRealtime() - checkStartTime > CHECK_TIME) {
                    val diff = currData.distance() - prevData.distance()
                    val minDiff = ViewConfiguration.get(view.context).scaledTouchSlop
                    checkState = if (diff > minDiff) {
                        STATE_CHECK_NOT_TROUGH
                    } else {
                        val checkRotation = getCheckRotation()
                        if (checkRotation >= CHECK_ROTATION) {
                            STATE_CHECK_TROUGH
                        } else {
                            STATE_CHECK_NOT_TROUGH
                        }
                    }
                }
            } else if (checkState == STATE_CHECK_TROUGH) {
                rotationCallback(getRotation())
            }
            if (ev.actionMasked == MotionEvent.ACTION_POINTER_UP && ev.actionIndex in 0..1) {
                if (checkState == STATE_CHECK_TROUGH) {
                    rotationEnd(getEndRotation())
                }
                resetPrevData()
            }
        }
    }

    private fun resetPrevData() {
        prevData = empty
        currData = empty
        checkState = STATE_CHECK_INIT
    }

    private fun getEndRotation(): Float {
        val rotation = getRotation()
        val endRotation = (((rotation.toInt() / 45) + 1) / 2) * 90f
        lastEndRotation = endRotation
        return endRotation
    }

    private fun getCheckRotation(): Float {
        val prevRotation = prevData.toDegrees()
        val currRotation = currData.toDegrees()
        val diff = currRotation - prevRotation
        val rotation = diff.toFloat()
        return abs(rotation)
    }

    private fun getRotation(): Float {
        val prevRotation = prevData.toDegrees()
        val currRotation = currData.toDegrees()
        val diff = currRotation - prevRotation
        val rotation = (diff.toFloat() + lastEndRotation) % 360
        return if (rotation < 0) {
            360 - rotation
        } else {
            rotation
        }
    }

    fun isInit() = checkState == STATE_CHECK_INIT

    fun isInProgress() = checkState == STATE_CHECK_DOING || checkState == STATE_CHECK_TROUGH

    private fun FloatArray.toDegrees(): Double {
        return Math.toDegrees(atan2(get(3) - get(1), get(2) - get(0)).toDouble()) * ROTATION_RATIO
    }

    private fun FloatArray.distance(): Float {
        return sqrt((get(0) - get(2)).pow(2) + (get(1) - get(3)).pow(2))
    }
}