/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MergeContentHelper.kt
 ** Description : MergeContentHelper.kt
 ** Version     : 1.0
 ** Date        : 2025/06/24
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/06/24      1.0      create
 ***********************************************************************/

package com.soundrecorder.translate.asr.realtime

import androidx.annotation.VisibleForTesting
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.ASR_RESULT_TYPE_INTERMEDIATE
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.ASR_RESULT_TYPE_VAD_FINAL

class MergeContentHelper {

    companion object {
        private const val PERCENTAGE_85 = 0.85
        private const val PERCENTAGE_115 = 1.15
    }

    // 当前语种的预期字数
    @VisibleForTesting
    internal var mExpectedWord: Int = LanguageUtil.DEFAULT_EXPECTED_WORDS

    // 当前分组列表，每个分组为一个List<ConvertContentItem>
    @VisibleForTesting
    internal val mGroupItemList: MutableList<MutableList<ConvertContentItem>> = mutableListOf()

    // 最终合并后的数据
    val mMergeItemList: MutableList<ConvertContentItem> = mutableListOf()

    /**
     * 根据语种获取该语种所预期的字数
     */
    fun refreshExpectedWordByLanguage(language: String) {
        mExpectedWord = LanguageUtil.getExpectedWords(language)
    }

    /**
     * 开始合并数据，实际分为两个步骤：
     * 1. 给传递过来的ConvertContentItem进行分组
     * 2. 将分组后的数据组装形成新的合并数据
     */
    fun mergeConvertItem(contentItem: ConvertContentItem) {
        // 给当前数据进行分组
        val isAddGroup = groupContent(contentItem)
        // 组装合并数据
        realMergeContent(isAddGroup)
    }

    /**
     * 给数据进行分组
     */
    @VisibleForTesting
    internal fun groupContent(contentItem: ConvertContentItem): Boolean {
        // 如果分组列表为空则直接新建一个分组
        if (mGroupItemList.isEmpty()) {
            mGroupItemList.add(mutableListOf(contentItem))
            return true
        }

        // 获取当前最后一个组，并明确将其作为可变列表来操作
        val lastGroup = mGroupItemList.last()

        // 如果新来的 item 与当前组最后一项为同一对象，则认为是数据更新（使用引用比较）
        if (lastGroup.isNotEmpty() && lastGroup.last() === contentItem) {
            // 如果当前组中只有一个，则不做任何合并拆分处理，保持现状即可
            if (lastGroup.size == 1) {
                return false
            }

            // 如果当前组的最后一项的状态为INTERMEDIATE，且当前组总字数>预期字数的115%，则拆分当前组，将当前组中的类型为INTERMEDIATE的分到下一组，其余Item留在当前组
            var currentGroupTextLength = 0
            lastGroup.forEach {
                currentGroupTextLength += it.textContent.length
            }
            if (lastGroup.last().textType == ASR_RESULT_TYPE_INTERMEDIATE && currentGroupTextLength >= mExpectedWord * PERCENTAGE_115) {
                // 当前组中不止一个Item，将状态为INTERMEDIATE放到下一组中，并在当前组中移除
                mGroupItemList.add(mutableListOf(lastGroup.last()))
                lastGroup.remove(lastGroup.last())
                return true
            }

            // 如果最后一个Item的roleId和上一个Item的roleId不一致，则直接将该Item从当前组中移除，放到一个新的组中
            if (lastGroup.last().textType == ASR_RESULT_TYPE_VAD_FINAL && lastGroup.last().roleId != lastGroup[lastGroup.size - 2].roleId) {
                mGroupItemList.add(mutableListOf(lastGroup.last()))
                lastGroup.remove(lastGroup.last())
                return true
            }
            return false
        } else {
            var currentGroupTextLength = 0
            lastGroup.forEach {
                currentGroupTextLength += it.textContent.length
            }
            // 如果当前分组的所有Item为文本长度之和大于等于预期字数的85%，或者新数据的roleId和当前分组的最后一个rolId不一致，则将新数据放到下一组
            if (currentGroupTextLength >= mExpectedWord * PERCENTAGE_85 || lastGroup.last().roleId != contentItem.roleId) {
                mGroupItemList.add(mutableListOf(contentItem))
                return true
            } else {
                // 添加到当前组中
                lastGroup.add(contentItem)
                return false
            }
        }
    }

    /**
     * 合并数据,一个组处理之后即为一个合并数据
     */
    private fun realMergeContent(isAddGroup: Boolean) {
        // 合并列表中没有数据,直接将当前分组中的数据添加进去
        if (mMergeItemList.isEmpty()) {
            mMergeItemList.add(generateMergeData(mGroupItemList.last()))
            return
        }

        // 没有新增组，则表示当前组更新数据.
        if (isAddGroup.not()) {
            updateMergeData(mMergeItemList.last(), mGroupItemList.last())
        } else {
            // 有新增组的时候，可能涉及重新分组，因此需要更新一下当前数据,前提是至少有两个数据
            if (mGroupItemList.size >= 2) {
                updateMergeData(mMergeItemList.last(), mGroupItemList[mGroupItemList.size - 2])
            }
            // 新增组，代表新增数据,需要新生成一个合并数据
            mMergeItemList.add(generateMergeData(mGroupItemList.last()))
        }
    }

    /**
     * 根据组数据，生成一个合并后的新数据
     */
    private fun generateMergeData(group: List<ConvertContentItem>): ConvertContentItem {
        val newMergeData = ConvertContentItem()
        realMergeGroupData(newMergeData, group)
        return newMergeData
    }

    /**
     * 根据组数据，更新合并后的数据
     */
    private fun updateMergeData(mergeData: ConvertContentItem, group: List<ConvertContentItem>) {
        realMergeGroupData(mergeData, group)
    }

    /**
     * 根据组数据，生成或更新合并后的数据
     */
    private fun realMergeGroupData(mergeData: ConvertContentItem, group: List<ConvertContentItem>) {
        val sb = StringBuilder()
        group.forEachIndexed { index, item ->
            // 先用第一个item的值先赋值
            if (index == 0) {
                mergeData.apply {
                    startTime = item.startTime
                }
            }

            // 合并文本
            sb.append(item.textContent)

            // 最后一个item的时候，做最后的合并处理
            if (index == group.size - 1) {
                mergeData.apply {
                    endTime = item.endTime
                    textContent = sb.toString()
                    roleId = item.roleId
                    roleName = item.roleName
                    textType = item.textType
                }
            }
        }
    }
}