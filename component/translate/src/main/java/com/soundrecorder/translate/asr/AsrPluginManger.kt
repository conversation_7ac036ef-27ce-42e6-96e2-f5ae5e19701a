/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: ConvertPluginManger
 * Description: ConvertPluginManger
 * Version: 1.0
 * Date: 2025/6/6
 * Author: W9017232
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9017232                         2025/6/6      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.translate.asr

import android.content.Context
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.modulerouter.smartname.IPluginDownloadCallback
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction
import com.soundrecorder.modulerouter.translate.IAsrDownloadCallback
import com.soundrecorder.modulerouter.translate.IAsrPluginCallBack
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.translate.AIAsrManager
import com.soundrecorder.translate.AIDownloadManager.Companion.DOWNLOAD_MODEL_CANCEL

class AsrPluginManger : IAsrPluginCallBack {

    companion object {
        private const val TAG = "AsrPluginManger"
    }

    private val aiAsrManagerAction by lazy {
        Injector.injectFactory<AIAsrManagerAction>()
    }

    /**
     * 下载 ai_asr_detector 插件
     *
     * @param context 上下文对象
     * @param callback 插件下载回调接口
     */
    override fun showAIAsrPluginsDialog(
        context: Context,
        callback: IPluginDownloadCallback?,
    ) {
        val applicationContext = context.applicationContext

        val isSupportAsr = AIAsrManager.isSupportAIAsr(context, true)
        DebugUtil.d(TAG, "showAiUnitPluginsDialog, isSupportAsr:$isSupportAsr")
        if (isSupportAsr) {
            //插件下载
            checkPluginsDownload(applicationContext, callback)
        } else {
            callback?.onDownLoadResult(false)
        }
    }

    override fun isAsrPluginSupportAndDownload(context: Context): Boolean {
        val isSupportAsr = AIAsrManager.isSupportAIAsr(context, true)
        DebugUtil.d(TAG, "isAsrPluginSupportAndDownload, isSupportAsr:$isSupportAsr")
        if (isSupportAsr.not()) {
            return false
        }
        val asrPluginDelegate = aiAsrManagerAction?.newAsrPluginDownloadDelegate()
        if (asrPluginDelegate == null) {
            DebugUtil.d(TAG, "isAsrPluginSupportAndDownload, asrPluginDelegate is null")
            return false
        }
        val asrPluginDownload = asrPluginDelegate.isAsrPluginDownload()
        DebugUtil.d(TAG, "isAsrPluginSupportAndDownload, asrPluginDownload:$asrPluginDownload")
        return asrPluginDownload
    }

    private fun checkPluginsDownload(context: Context, callback: IPluginDownloadCallback?) {
        val asrPluginDelegate = aiAsrManagerAction?.newAsrPluginDownloadDelegate()
        if (asrPluginDelegate == null) {
            DebugUtil.d(TAG, "checkPluginsDownload, asrPluginDelegate is null")
            callback?.onDownLoadResult(false)
            return
        }
        val asrPluginDownload = asrPluginDelegate.isAsrPluginDownload()
        DebugUtil.d(TAG, "checkPluginsDownload, asrPluginDownload:$asrPluginDownload")
        if (!asrPluginDownload) {
            if (NetworkUtils.isNetworkInvalid(context)) {
                ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
                callback?.onDownLoadResult(false)
                return
            }
            asrPluginDelegate.checkAndDownloadAsrPlugin(context, false, object : IAsrDownloadCallback {
                override fun downloadSuccess(sceneName: String) {
                    DebugUtil.d(TAG, "checkPluginsDownload, downloadSuccess:")
                    callback?.onDownLoadResult(true)
                }

                override fun downloadFail(errorMessage: String) {
                    DebugUtil.d(TAG, "checkPluginsDownload, downloadFail:$errorMessage")
                    if (errorMessage != DOWNLOAD_MODEL_CANCEL) {
                        ToastManager.showShortToast(context, com.soundrecorder.common.R.string.smart_aiunit_plugin_download_failed_retry)
                    }
                    callback?.onDownLoadResult(false)
                }
            })
        } else {
            callback?.onDownLoadResult(true)
        }
    }
}