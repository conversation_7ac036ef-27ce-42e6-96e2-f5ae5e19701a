/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecordNotificationProcessorTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager

import android.content.BroadcastReceiver
import android.content.Intent
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class RecordNotificationProcessorTest {
    @Test
    fun should_success_when_getNotificationModel() {
        val notificationProcessor = RecordNotificationProcessor()
        val model = Whitebox.invokeMethod<NotificationModel>(notificationProcessor, "getNotificationModel", RecorderService(), false)
        Assert.assertNotNull(model)
    }

    @Test
    fun should_success_when_registerNotificationBroadcast() {
        val notificationProcessor = RecordNotificationProcessor()
        Whitebox.invokeMethod<NotificationModel>(notificationProcessor, "registerNotificationBroadcast", RecorderService(), false)
        val receiver = Whitebox.getInternalState<BroadcastReceiver>(notificationProcessor, "mNotificationReceiver")
        val intent = Intent().also {
            it.action = NotificationUtils.PLAY_STATUS_CHANGED_ACTION
        }
        receiver.onReceive(BaseApplication.getAppContext(), intent)
        Assert.assertNotNull(receiver)
    }
}