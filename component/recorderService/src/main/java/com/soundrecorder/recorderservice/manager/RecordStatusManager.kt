/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordStatusManager
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import androidx.annotation.IntDef
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.DebugUtil

object RecordStatusManager {

    const val TAG = "RecordStatusManager"

    const val INIT = -1
    const val HALT_ON = 0
    const val RECORDING = 1
    const val PAUSED = 2

    private const val LAST_INIT = -2
    private var mCurrentStatus = SyncMutableLiveData<Int>()
    private var mLastCurrentStatus: Int = LAST_INIT
    private var mRecordStatusChangeListener: OnRecordStatusChangeListener? = null
    private var mCurrentTimeMillis = MutableLiveData<Long>()

    @JvmStatic
    fun changeRecordStatus(newStatus: Int) {
        DebugUtil.i(
            TAG, "changeRecordStatus newStatus : $newStatus, currentStatus: $mCurrentStatus, mLastCurrentStatus: $mLastCurrentStatus"
        )
        if (newStatus != mCurrentStatus.syncValue) {
            mLastCurrentStatus = mCurrentStatus.syncValue ?: INIT
            mCurrentStatus.postValueSafe(newStatus)
            mRecordStatusChangeListener?.onRecordStatusChange(newStatus, mLastCurrentStatus)
        }
    }

    @JvmStatic
    fun setCurrentStatus(state: Int) {
        mCurrentStatus.postValueSafe(state)
    }

    @JvmStatic
    @RecordStatus
    fun getCurrentStatus(): Int = mCurrentStatus.getValueSync() ?: INIT

    @JvmStatic
    fun getLastStatus(): Int = mLastCurrentStatus

    @JvmStatic
    fun getCurrentStatusLiveData(): MutableLiveData<Int> = mCurrentStatus

    @JvmStatic
    fun isAlreadyRecording(): Boolean {
        return getCurrentStatus() == RECORDING || getCurrentStatus() == PAUSED
    }

    fun resetState() {
        mCurrentStatus.postValueSafe(null)
        mLastCurrentStatus = LAST_INIT
        mCurrentTimeMillis.postValueSafe(0)
        DebugUtil.i(TAG, "------state resetState")
    }

    fun registerChangeListener(onRecordStatusChangeListener: OnRecordStatusChangeListener?) {
        mRecordStatusChangeListener = onRecordStatusChangeListener
    }

    fun unRegisterChangeListener() {
        mRecordStatusChangeListener = null
    }

    @JvmStatic
    fun setCurrentTimeMillis(curTimeMillis: Long) {
        mCurrentTimeMillis.postValueSafe(curTimeMillis)
    }

    @JvmStatic
    fun getCurrentTimeMillisLiveData(): MutableLiveData<Long> {
        return mCurrentTimeMillis
    }

    interface OnRecordStatusChangeListener {
        fun onRecordStatusChange(currentStatus: Int, lastStatus: Int)
    }
}

@IntDef(RecordStatusManager.INIT, RecordStatusManager.HALT_ON, RecordStatusManager.RECORDING, RecordStatusManager.PAUSED)
@Retention(AnnotationRetention.SOURCE)
annotation class RecordStatus