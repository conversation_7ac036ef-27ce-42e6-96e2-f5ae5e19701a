/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AndroidMediaRecorder
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.recorder

import android.media.MediaRecorder
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.recorderservice.recorder.listener.IBizRecorder
import com.soundrecorder.recorderservice.recorder.listener.IOnErrorListener
import com.soundrecorder.recorderservice.recorder.listener.IOnInfoListener
import com.soundrecorder.recorderservice.recorder.utils.SimpleTimer
import java.io.FileDescriptor

class AndroidMediaRecorder(format: Int) : MediaRecorder(), IBizRecorder {
    private var mSimpleTimer: SimpleTimer? = null

    @Volatile
    private var mAudioSourceSetted = false
    private var mSuffix = ""
    private var mMimeType = ""
    // 记录录制过程中的暂停，便于，继续录制调用resume方法
    // true: 暂停录制状态 false：默认状态
    private var mIsPauseState = false

    init {
        mSimpleTimer =
            SimpleTimer()
        when (format) {
            RecorderConstant.RECORDER_AUDIO_FORMAT_WAV -> initWavFile()
            RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS -> initAACFile()
            else -> initWavFile()
        }
    }

    //Incorrect constant
    private fun initWavFile() {
        /*setAudioSource(AudioSource.MIC)
        setOutputFormat(RecorderConstant.OP_WAV_OUTPUT_FORMAT)
        setAudioSamplingRate(RecorderConstant.SAMPLE_RATE_44100)
        setAudioChannels(2)
        setAudioEncoder(RecorderConstant.OP_WAV_ENCODER)

        mSuffix = RecorderConstant.WAV_FILE_SUFFIX
        mMimeType = RecordModelConst.MIMETYPE_WAV

        setMaxDuration(RecorderConstant.MAX_DURATION_WAV_MS)*/
    }

    private fun initAACFile() {
        setAudioSource(AudioSource.MIC)
        setOutputFormat(OutputFormat.AAC_ADTS)
        setAudioSamplingRate(RecorderConstant.SAMPLE_RATE_44100)
        setAudioChannels(2)
        setAudioEncoder(AudioEncoder.AAC)
        setAudioEncodingBitRate(RecorderConstant.OP_ENCODE_BITRATE_AAC)

        mSuffix = RecorderConstant.AAC_FILE_SUFFIX
        mMimeType = RecordConstant.MIMETYPE_ACC
    }

    override fun expandFile(path: String, audioSource: Int) {
        mAudioSourceSetted = true
    }

    override fun expandFile(fd: FileDescriptor, offset: Long, length: Long, audioSource: Int) {
        mAudioSourceSetted = true
    }

    override fun start() {
        if (mIsPauseState && !isPausedByResetRecorder()) {
            // 暂停后，继续录制
            super.resume()
        } else {
            super.start()
        }
        mIsPauseState = false
        mSimpleTimer?.start()
    }

    override fun setAppendTime(time: Long) {
        mSimpleTimer?.setAppendTime(time)
    }

    override fun stop() {
        mAudioSourceSetted = false
        super.stop()
        mSimpleTimer?.stop()
        reset()
    }

    /**
     * 暂停，使用mediaRecorder.pause实现
     * 继续录制：使用使用mediaRecorder.resume实现
     */
    override fun stopForPause() {
        if (isPausedByResetRecorder()) {
            super.stop()
            mAudioSourceSetted = false
            if (!mSimpleTimer!!.isStopped) {
                mSimpleTimer!!.pause()
                super.reset()
            }
        } else {
            super.pause()
            mIsPauseState = true
            mAudioSourceSetted = false
            if (mSimpleTimer?.isStopped == false) {
                mSimpleTimer?.pause()
            }
        }
    }

    override fun getTime(): Long {
        return mSimpleTimer?.time ?: 0L
    }

    override fun setAudioSource(audioSource: Int) {
        super.setAudioSource(audioSource)
        mAudioSourceSetted = true
    }

    override fun getRecorderSuffix(): String = mSuffix

    override fun getRecorderMimeType(): String = mMimeType

    override fun isPausedByResetRecorder(): Boolean  = false

    override fun reset() {
        mSimpleTimer?.reset()
        mAudioSourceSetted = false
        mIsPauseState = false
        super.reset()
    }

    // make it safer
    override fun getMaxAmplitude(): Int {
        return if (mAudioSourceSetted) {
            super.getMaxAmplitude()
        } else {
            0
        }
    }

    override fun setOnInfoListener(listener: IOnInfoListener?) {
        super.setOnInfoListener(listener)
    }

    override fun setOnErrorListener(listener: IOnErrorListener?) {
        super.setOnErrorListener(listener)
    }

    override fun getMediaRecorder(): MediaRecorder? {
        return this
    }
}