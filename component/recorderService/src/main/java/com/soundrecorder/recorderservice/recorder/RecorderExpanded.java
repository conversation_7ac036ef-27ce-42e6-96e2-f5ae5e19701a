/************************************************************
 * All rights reserved.
 * FileName      : RecorderExpanded.java
 * Version Number: 1.0
 * Description   : extends MediaRecorder with method getTime, pause, resume, setAudioQuality
 * Author        : zhanghr
 * Date          : 2009-12-1
 * History       :(ID,  2009-12-1, zhanghr, Description)
 ************************************************************/

package com.soundrecorder.recorderservice.recorder;

import android.media.MediaRecorder;
import androidx.annotation.NonNull;
import org.jetbrains.annotations.Nullable;
import java.io.FileDescriptor;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.constant.RecorderConstant;
import com.soundrecorder.recorderservice.recorder.listener.IBizRecorder;
import com.soundrecorder.recorderservice.recorder.listener.IOnErrorListener;
import com.soundrecorder.recorderservice.recorder.listener.IOnInfoListener;
import com.oppo.media.OppoRecorder;
import com.soundrecorder.recorderservice.recorder.utils.SimpleTimer;

public class RecorderExpanded extends OppoRecorder implements IBizRecorder {
    private static final String TAG = "RecorderWithTimer";
    private SimpleTimer mSimpleTimer;
    private volatile boolean mAudioSourceSetted = false;
    private String mSuffix = "";
    private String mMimeType = "";


    public RecorderExpanded() {
        this(OutputFormat.MP3);
    }

    public RecorderExpanded(int format) {
        super();
        mSimpleTimer = new SimpleTimer();
        switch (format) {
            case RecorderConstant.RECORDER_AUDIO_FORMAT_MP3:
                initMp3Config();
                break;
            case RecorderConstant.RECORDER_AUDIO_FORMAT_WAV:
                initWavConfig();
                break;
            case RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_NB:
                initAmrConfig();
                break;
            default:
                initMp3Config();
                break;
        }

    }

    private void initMp3Config() {
        setAudioSource(OppoRecorder.AudioSource.MIC);
        setAudioSamplingRate(RecorderConstant.SAMPLE_RATE_48000);
        setAudioChannels(2);
        setOutputFormat(OppoRecorder.OutputFormat.MP3);
        setAudioEncoder(OppoRecorder.AudioEncoder.MPEG);
        setAudioEncodingBitRate(320); //change form 320 to 128 to reduce the size of MP3 file
        mSuffix = RecorderConstant.MP3_FILE_SUFFIX;
        mMimeType = RecordConstant.MIMETYPE_MP3;
    }

    private void initAmrConfig() {
        setAudioSource(OppoRecorder.AudioSource.MIC);
        setOutputFormat(OppoRecorder.OutputFormat.AMR_NB);
        setAudioEncoder(OppoRecorder.AudioEncoder.AMR_NB);
        mSuffix = RecorderConstant.AMR_FILE_SUFFIX;
        mMimeType = RecordConstant.MIMETYPE_AMR;
    }

    private void initWavConfig() {
        setAudioSource(OppoRecorder.AudioSource.MIC);
        setOutputFormat(OppoRecorder.OutputFormat.WAV);
        setAudioSamplingRate(RecorderConstant.SAMPLE_RATE_48000); //change sampling rate to 16KHz,8000 16000 44100 48000
        setAudioChannels(2);
        setAudioEncoder(OppoRecorder.AudioEncoder.WAV);
        mSuffix = RecorderConstant.WAV_FILE_SUFFIX;
        mMimeType = RecordConstant.MIMETYPE_WAV;
    }

    @Override
    public void expandFile(String path, int as) {
        mAudioSourceSetted = true;
        super.expandFile(path, as);
    }

    @Override
    public void expandFile(FileDescriptor fd, long offset, long length, int as) {
        mAudioSourceSetted = true;
        super.expandFile(fd, offset, length, as);
    }

    @Override
    public void start() {
        super.start();
        mSimpleTimer.start();
    }

    public void setAppendTime(long time) {
        mSimpleTimer.setAppendTime(time);
    }

    @Override
    public void stop() {
        mAudioSourceSetted = false;
        super.stop();
        mSimpleTimer.stop();
        reset();
    }

    public void stopForPause() {
        super.stop();
        mAudioSourceSetted = false;
        if (!mSimpleTimer.isStopped()) {
            mSimpleTimer.pause();
            super.reset();
        }
    }

    /*
     * Be careful ! If this method is named pause, an error of stack over flow
     * will occur .
     */
    public void pauseNow() {
        mSimpleTimer.pause();
        try {
            OppoRecorder.class.getMethod("pause").invoke(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*
     * Be careful ! If this method is named resume, an error of stack over flow
     * will occur .
     */
    public void resumeNow() {

        mSimpleTimer.resume();

        try {
            OppoRecorder.class.getMethod("resume").invoke(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public long getTime() {
        return mSimpleTimer.getTime();
    }


    public void setFormatToWav() {

    }

    /*
     * Be careful ! If this method is named setAudioQuality, an error of stack
     * over flow will occur .
     */
    public void setAudioQualityNow(int quality) {
        try {
            OppoRecorder.class.getMethod("setAudioQuality", int.class).invoke(this, quality);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setAudioSource(int audioSource) {
        super.setAudioSource(audioSource);
        mAudioSourceSetted = true;
    }

    @Override
    public void reset() {
        mSimpleTimer.reset();
        mAudioSourceSetted = false;
        super.reset();
    }

    // make it safer
    @Override
    public int getMaxAmplitude() {
        if (mAudioSourceSetted) {
            return super.getMaxAmplitude();
        } else {
            return 0;
        }
    }

    @Override
    public void setOnInfoListener(@Nullable IOnInfoListener listener) {
        super.setOnInfoListener(listener);
    }

    @Override
    public void setOnErrorListener(@Nullable IOnErrorListener listener) {
        super.setOnErrorListener(listener);
    }

    @NonNull
    @Override
    public String getRecorderSuffix() {
        return mSuffix;
    }

    @NonNull
    @Override
    public String getRecorderMimeType() {
        return mMimeType;
    }

    @Override
    public boolean isPausedByResetRecorder() {
        return true;
    }

    @Override
    public MediaRecorder getMediaRecorder() {
        return null;
    }
}
