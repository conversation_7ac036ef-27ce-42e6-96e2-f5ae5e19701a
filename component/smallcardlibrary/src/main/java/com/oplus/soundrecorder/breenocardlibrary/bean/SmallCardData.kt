/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardData
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.bean

import androidx.annotation.Keep

@Keep
internal data class SmallCardData(
    val packageName: String,
    val widgetCode: String,
    var recordState: Int, // 录制状态值
    var saveFileState: Int, // 保存状态值
    val timeText: String, // 卡片录制时长TEXT
    val timeTextColor: Int, // 卡片录制时长TEXT COLOR
    val timeDes: String, // 卡片录制时长TEXT TALKBACK
    val stateText: String, // 卡片保存成功VIEW的TEXT
    val showMarkNotice: <PERSON>ole<PERSON>,
    val markEnable: Boolean,
    val isStartServiceFormAppCar: Boolean,
    val fileName: String, // 卡片保存成功VIEW的文件名称
    val fileNameWithOutType: String, // 文件名称不带后缀名
    var markSrc: Int, // 卡片标记按钮资源ID
    var markBackGroundSrc: Int,
    var saveFileSrc: Int, // 卡片保存按钮资源ID
    var saveBackGroundSrc: Int,
    var recordInitSrc: Int, // 卡片录制按钮默认未录制资源ID
    val recordPauseSrc: Int, // 卡片录制按钮录制暂停资源ID
    val recordResumeSrc: Int, // 卡片录制按钮录制中资源ID
    val waveData: Int,
    val showLoadingDialog: Boolean, // 是否显示loading
    val progressValue: Int, // 进度值（预估值）。
    var loadingTitle: String, // 卡片loadingView 的TEXT
    var needRunMarkAndSaveAnimation: Boolean = false,
    val waveRadius: Float = -1F,
    val animatorDuration: Long = -1,
    val ampMinHeight: Int = -1,
    val ampMaxHeight: Int = -1,
    val ampColor: String = "",
    val ampMaxRandomHeight: Int = -1,
    val recordServiceUUID: String = "",
    val markDesc: String? = "",
    val recordStateDesc: String? = "",
    val saveDesc: String? = "",
    val recordTimeMill: Long = 0, // 录制时长毫秒，add in versionCode 3
    val lastMarks: List<MarkDataBean>? = null, // 标记数据，add in versionCode 3
    val lastAmps: List<Int>? = null, // 波形数据，add in versionCode 3
    val ampTotalSize: Int = 0, // 波形总数量，add in versionCode 3
    val cardBgDrawableRes: Int = -1, // 卡片背景颜色，add in versionCode 3
    val loadingTitleTalkBack: String = "", // 保存中TEXT的talkback描述，add in versionCode 3
    val versionCode: Int = -1, // 录音app中卡片版本，add in versionCode 3
)

object RecorderState {
    const val INIT = 0
    const val RECORDING = 1
    const val PAUSED = 2
}

object SaveFileState {
    const val INIT = 0
    const val START_LOADING = 1
    const val SHOW_DIALOG = 2
    const val SUCCESS = 3
    const val ERROR = 4
}

object ClickAction {
    const val CARD_SWITCH_RECORDER_STATUS = "switch_recorder_status"
    const val CARD_ADD_TEXT_MARK = "add_text_mark"
    const val CARD_SAVE_RECORDER_FILE = "save_recorder_file"
    const val CHECK_RECORDER_PERMISSION = "check_recorder_permission"
    const val CHECK_CAN_START_SERVICE = "check_can_start_service"
    const val CLEAR_ALL_TASK = "clear_all_Task"
}
