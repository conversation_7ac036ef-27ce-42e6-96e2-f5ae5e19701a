
/************************************************************
 * Copyright 2000-2009 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : SpeakerStateManager.java
 * Version Number: 1.0
 * Description   :
 * Author        : SpeakerStateManager
 * Date          : 2020-03-28
 * History       :(ID,  2020-03-28, lzs, Description)
 ************************************************************/
package com.soundrecorder.player.speaker;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.media.AudioManager;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.PrefUtil;
import com.soundrecorder.base.utils.DebugUtil;

public class SpeakerStateManager {

    private static final String TAG = "SpeakerStateManager";

    // SP State
    public static final int SP_SPEAKER_ON = 0;
    public static final int SP_SPEAKER_OFF = 2;

    // UI State
    public static final int SPEAKER_ON_WITHOUT_HEADSET = 0;
    public static final int SPEAKER_ON_WITHIN_HEADSET = 1;
    public static final int SPEAKER_OFF_WITHOUT_HEADSET = 2;
    public static final int SPEAKER_OFF_WITHIN_HEADSET = 3;

    private static SpeakerStateManager sInstance = null;
    private boolean mIsHeadSetPluged = false;

    public static synchronized SpeakerStateManager getInstance() {
        if (sInstance == null) {
            sInstance = new SpeakerStateManager();
        }
        return sInstance;
    }

    private SpeakerStateManager() {
        initSpeakerState();
    }

    public void initSpeakerState() {
        mIsHeadSetPluged = isAnyHeadsetOn();
    }


    public static boolean isAnyHeadsetOn() {
        boolean isAnyHeadsetOn = isBlueHeadsetOn() || isWiredHeadsetOn();
        DebugUtil.d(TAG, isAnyHeadsetOn + "");
        return isAnyHeadsetOn;
    }

    public static boolean isWiredHeadsetOn() {
        boolean result = false;
        AudioManager mAudioManagerTemp = (AudioManager) BaseApplication.getAppContext().getSystemService(Context.AUDIO_SERVICE);
        if (mAudioManagerTemp != null) {
            result = mAudioManagerTemp.isWiredHeadsetOn();
        }
        DebugUtil.i(TAG, "isWiredHeadsetOn: " + result);
        return result;
    }


    @SuppressLint("MissingPermission")
    public static boolean isBlueHeadsetOn() {
        int headset = -1;
        if (!BaseUtil.isAndroidSOrLater()) {
            BluetoothAdapter defaultAdapter = BluetoothAdapter.getDefaultAdapter();
            if (defaultAdapter != null) {
                try {
                    headset = defaultAdapter.getProfileConnectionState(BluetoothProfile.HEADSET);
                } catch (Throwable e) {
                    DebugUtil.e(TAG, "androidS bluetooth permission error" + e);
                }
            }
        }
        AudioManager mAudioManagerTemp = (AudioManager) BaseApplication.getAppContext().getSystemService(Context.AUDIO_SERVICE);
        if (mAudioManagerTemp != null) {
            boolean isBluetoothScoOn = mAudioManagerTemp.isBluetoothScoOn();
            boolean isBluetoothA2dpOn = mAudioManagerTemp.isBluetoothA2dpOn();
            boolean isStateConnected = (headset == BluetoothProfile.STATE_CONNECTED);
            boolean result = (isBluetoothScoOn || isBluetoothA2dpOn || isStateConnected);
            DebugUtil.i(TAG, "isBlueHeadsetOn: " + result + "; isBluetoothScoOn: " + isBluetoothScoOn + "; isBluetoothA2dpOn: " + isBluetoothA2dpOn + " isStateConnected：" + isStateConnected);
            return result;
        }
        DebugUtil.i(TAG, "isBlueHeadsetOn: false");
        return false;
    }

    public void setSpeakerOnSp() {
        PrefUtil.putInt(BaseApplication.getAppContext(), PrefUtil.SPEAKER_STATE, SP_SPEAKER_ON);
    }

    public void setSpeakerOffSp() {
        PrefUtil.putInt(BaseApplication.getAppContext(), PrefUtil.SPEAKER_STATE, SP_SPEAKER_OFF);
    }


    public int getSpeakerSp() {
        return PrefUtil.getInt(BaseApplication.getAppContext(), PrefUtil.SPEAKER_STATE, SP_SPEAKER_ON);
    }


    public boolean isHeadSetPluged() {
        return mIsHeadSetPluged;
    }

    public void setIsHeadSetPluged(boolean mIsHeadSetPluged) {
        this.mIsHeadSetPluged = mIsHeadSetPluged;
    }


    public int getNeedAudioStreamType() {
        /*don't control by headset on androidS or later*/
        if (BaseUtil.isAndroidSOrLater()) {
            return isSpeakerOff() ? AudioManager.STREAM_VOICE_CALL : AudioManager.STREAM_MUSIC;
        }
        /**below androidS still keep origin logic*/
        if (isSpeakerOff() && (!SpeakerStateManager.getInstance().isHeadSetPluged())) {
            DebugUtil.i(TAG, "===>STREAM_VOICE_CALL");
            return AudioManager.STREAM_VOICE_CALL;
        } else {
            DebugUtil.i(TAG, "===>STREAM_MUSIC");
            return AudioManager.STREAM_MUSIC;
        }
    }


    public boolean isSpeakerOff() {
        int preSpeakerSpStatus = SpeakerStateManager.getInstance().getSpeakerSp();
        DebugUtil.i(TAG, "preProcessWhenRequestFocus: preSpeakerSpStatus " + preSpeakerSpStatus);
        if ((preSpeakerSpStatus == SP_SPEAKER_OFF)) {
            return true;
        }
        return false;
    }
}
