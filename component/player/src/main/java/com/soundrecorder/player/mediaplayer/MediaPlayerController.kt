package com.soundrecorder.player.mediaplayer

import android.content.Context
import android.media.AudioManager
import android.media.MediaPlayer
import android.net.Uri
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.player.base.IPlayerCallback
import com.soundrecorder.player.base.IPlayerController
import com.soundrecorder.player.status.PlayStatus

class MediaPlayerController(private val playerCallback: IPlayerCallback?) : IPlayerController,
    MediaPlayer.OnCompletionListener, AudioManager.OnAudioFocusChangeListener, MediaPlayer.OnErrorListener {

    private var playUri: Uri? = null

    private var mPlayer: MediaPlayer? = null
    private var mAudioManager: AudioManager? = null
    private var mCurrentStreamType: Int = AudioManager.STREAM_MUSIC
    private var mCurrentSpeed: Float = 1f

    companion object {
        private const val TAG = "MediaPlayerController"
    }

    private fun initAudioManager() {
        if (mAudioManager == null) {
            DebugUtil.i(TAG, "initAudioManager")
            mAudioManager = BaseApplication.getAppContext()
                .getSystemService(Context.AUDIO_SERVICE) as AudioManager
        }
    }

    private fun initMediaPlayer() {
        DebugUtil.i(TAG, "initMediaPlayer")
        mPlayer = MediaPlayer().also {
            it.setOnErrorListener(this)
            it.setOnCompletionListener(this)
        }
    }

    override fun setPlayUri(playUri: Uri?) {
        this.playUri = playUri
    }

    override fun getPlayUri(): Uri? {
        return playUri
    }

    override fun getAudioStreamType(): Int {
        return playerCallback?.getAudioStreamType()?.also {
            mCurrentStreamType = it
        } ?: mCurrentStreamType
    }

    override fun onCompletion(player: MediaPlayer?) {
        DebugUtil.d(TAG, "onCompletion ACTION_COMPLETE")
        playerCallback?.onActionComplete(PlayStatus.ACTION_COMPLETE)
        releasePlay()
    }

    override fun onAudioFocusChange(focusChange: Int) {
        DebugUtil.i(TAG, "focusChange$focusChange")
        if (focusChange == AudioManager.AUDIOFOCUS_LOSS || focusChange == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
            DebugUtil.i(TAG, "audio focus changed play to pause!")
            pausePlay()
        } else {
            DebugUtil.i(TAG, "focus changed: $focusChange")
        }
    }

    override fun onError(player: MediaPlayer?, what: Int, extra: Int): Boolean {
        mediaPlayerError(extra)
        return false
    }

    override fun pausePlay() {
        if (!isPlaying()) {
            DebugUtil.i(TAG, "player is not playing no need pause")
            return
        }

        try {
            DebugUtil.i(TAG, "pausePlayer")
            mPlayer!!.pause()
            playerCallback?.setPlayerStatus(PlayStatus.PLAYER_STATE_PAUSE)
            mAudioManager?.abandonAudioFocus(this)
            playerCallback?.onActionComplete(PlayStatus.ACTION_PAUSE)
        } catch (e: Exception) {
            DebugUtil.w(TAG, "pausePlay error is $e")
        }
    }

    override fun continuePlay() {
        try {
            requestAudioFocus()
            resetSpeakerPhoneOn()
            mPlayer!!.start()
            playerCallback?.setPlayerStatus(PlayStatus.PLAYER_STATE_PLAYING)
            mPlayer!!.setOnCompletionListener(this)
            mPlayer!!.setVolume(1f, 1f)
            playerCallback?.onActionComplete(PlayStatus.ACTION_CONTINUE)
        } catch (e: Exception) {
            DebugUtil.w(TAG, "continuePlay error is $e")
        }
    }

    override fun startToPlay() {
        DebugUtil.i(TAG, "startToPlay")
        requestAudioFocus()
        try {
            resetSpeakerPhoneOn()
            mPlayer!!.start()
            playerCallback?.setPlayerStatus(PlayStatus.PLAYER_STATE_PLAYING)
            mPlayer!!.setVolume(1f, 1f)
            playerCallback?.onActionComplete(PlayStatus.ACTION_PLAY)
            DebugUtil.i(TAG, "startToPlay end")
        } catch (ex: Exception) {
            DebugUtil.w(TAG, "startToPlay error: $ex")
            mediaPlayerError()
        }
    }

    override fun seekToPlay(timeMillis: Long) {
        if (mPlayer == null) {
            startPlay(timeMillis, 0)
        } else {
            seekTime(timeMillis)
            continuePlay()
        }
    }

    override fun startPlay(timeMillis: Long, delay: Long) {
        val prepared = prepareToPlay(timeMillis)
        if (prepared) {
            if (delay > 0) {
                try {
                    Thread.sleep(delay)
                } catch (ignored: InterruptedException) {
                }
            }
            startToPlay()
        }
    }

    override fun prepareToPlay(timeMillis: Long): Boolean {
        if (isPlaying()) {
            DebugUtil.w(TAG, "prepareToPlay isPlaying, no need to prepare")
            return false
        }

        if (playUri == null) {
            DebugUtil.w(TAG, "prepareToPlay uri = null")
            return false
        }

        if (mPlayer == null) {
            initMediaPlayer()
        }
        try {
            val currentTime = System.currentTimeMillis()
            mPlayer!!.reset()
            DebugUtil.i(TAG, "setDataSource uri: $playUri")
            mPlayer!!.setAudioStreamType(getAudioStreamType())
            mPlayer!!.setDataSource(BaseApplication.getAppContext(), playUri!!)
            changePlayerSpeed(mCurrentSpeed, false)
            mPlayer!!.prepare()
            playerCallback?.setDuration(mPlayer!!.duration.toLong())

            DebugUtil.d(TAG, "prepareToPlay player seek to = $timeMillis")
            if (timeMillis >= 0) {
                mPlayer!!.seekTo(timeMillis.toInt())
            }

            playerCallback?.onActionComplete(PlayStatus.ACTION_PREPARE)
            DebugUtil.i(
                TAG,
                "get Duration time is : ${mPlayer!!.duration.toLong()}, mPlayer prepared delta time is : " + (System.currentTimeMillis() - currentTime))
        } catch (ex: Exception) {
            DebugUtil.w(TAG, "start play error: $ex")
            mediaPlayerError()
            return false
        }

        return true
    }

    private fun mediaPlayerError(extra: Int = Int.MAX_VALUE) {
        playerCallback?.onPlayError(extra)
    }

    private fun resetSpeakerPhoneOn() {
        if (mAudioManager != null && mAudioManager!!.isSpeakerphoneOn) {
            mAudioManager!!.isSpeakerphoneOn = false
        }
    }

    private fun requestAudioFocus() {
        initAudioManager()
        getAudioStreamType().also {
            DebugUtil.i(TAG, "requestAudioFocus: audioStreamType = $it")
            mAudioManager?.requestAudioFocus(this, it, AudioManager.AUDIOFOCUS_GAIN)
        }
    }

    override fun changePlayerSpeed(speed: Float, isSilence: Boolean) {
        mCurrentSpeed = speed

        if (mPlayer == null) {
            return
        }
        try {
            val isPlaying: Boolean = mPlayer!!.isPlaying
            if (!isPlaying && isSilence) {
                mPlayer!!.setVolume(0f, 0f)
            } else {
                mPlayer!!.setVolume(1f, 1f)
            }

            mPlayer!!.playbackParams.let {
                mPlayer!!.playbackParams = it.setSpeed(speed)
            }
            if (!isPlaying && isSilence) {
                mPlayer!!.pause()
            }
        } catch (e: Throwable) {
            DebugUtil.w(TAG, "changePlayerSpeed error: $e")
        }
    }

    override fun getCurrentPosition(): Long {
        return try {
            mPlayer?.currentPosition?.toLong() ?: 0L
        } catch (e: Exception) {
            DebugUtil.w(TAG, "getCurrentPosition: $e")
            0L
        }
    }

    override fun seekTime(timeMills: Long) {
        try {
            DebugUtil.d(TAG, "seekTime: $timeMills")
            mPlayer?.seekTo(timeMills.toInt())
        } catch (e: Exception) {
            DebugUtil.w(TAG, "seekTime: $e")
        }
    }

    override fun toggleAudioStream(timeMillis: Long) {
        if (mCurrentStreamType == getAudioStreamType()) {
            DebugUtil.w(TAG, "no need reset AudioStreamType!")
            return
        }

        playerCallback?.onActionComplete(PlayStatus.ACTION_REPLAY_START)
        if (isPlaying()) {
            release()
            startPlay(timeMillis, 0)
        } else {
            release()
            prepareToPlay(timeMillis)
        }
        playerCallback?.onActionComplete(PlayStatus.ACTION_REPLAY_END)
    }

    @Suppress("TooGenericExceptionCaught")
    override fun isPlaying(): Boolean {
        return try {
            mPlayer?.isPlaying == true
        } catch (e: Exception) {
            DebugUtil.w(TAG, "isPlaying: $e")
            false
        }
    }

    override fun loadDuration(): Long {
        DebugUtil.i(TAG, "loadDuration")
        var duration = 0L
        val player = MediaPlayer()
        try {
            player.reset()
            player.setDataSource(BaseApplication.getAppContext(), playUri!!)
            player.prepare()
            duration = player.duration.toLong()
            playerCallback?.setDuration(duration)
        } catch (ex: Exception) {
            DebugUtil.w(TAG, "loadDuration error: $ex")
            mediaPlayerError()
        } finally {
            player.release()
        }
        return duration
    }

    override fun releasePlay() {
        DebugUtil.d(TAG, "releasePlay ")
        try {
            mPlayer?.stop()
            mPlayer?.release()
            playerCallback?.onActionComplete(PlayStatus.ACTION_RELEASE)
        } catch (e: Exception) {
            DebugUtil.w(TAG, "releasePlay error: $e")
        } finally {
            mPlayer = null
        }
        mAudioManager?.abandonAudioFocus(this)
    }

    private fun release() {
        mPlayer?.release()
        playerCallback?.onActionComplete(PlayStatus.ACTION_RELEASE)
        mPlayer = null
    }

    override fun onRelease() {
    }
}