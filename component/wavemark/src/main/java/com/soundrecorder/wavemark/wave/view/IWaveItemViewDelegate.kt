/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IWaveItemViewDelegate
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.wave.view

import android.content.Context
import android.view.ViewGroup

interface IWaveItemViewDelegate<T : WaveItemView> {

    fun createNewItemView(context: Context, parent: ViewGroup): T

    fun fixItemCount(totalCount: Int): Int

    fun onItemViewCreated(parent: ViewGroup, rulerView: T)

    fun onBindItemView(rulerView: T, position: Int)
}