package com.soundrecorder.wavemark.mark;


import com.soundrecorder.base.utils.DebugUtil;

public class MarkerCounter {

    private final static String TAG = "MarkerCounter";
    private final static int DEFAULT_INDEX = 0;
    private int startIndex = DEFAULT_INDEX;

    private MarkerCounter() {
    }

    public static MarkerCounter newInstance() {
        return new MarkerCounter();
    }

    public int getNext() {
        startIndex++;
        DebugUtil.d(TAG, "next = " + startIndex);
        return startIndex;
    }

    public void setStartIndex(int input) {
        startIndex = input;
        DebugUtil.d(TAG, "startIndex = " + startIndex);
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void reset() {
        startIndex = DEFAULT_INDEX;
    }

}
